"""
Alpha因子库
实现200+个经典量化因子，符合生产环境标准
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings
from scipy import stats
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
import talib

class AlphaFactorLibrary:
    """
    Alpha因子库
    实现经典量化因子和机器学习因子
    """

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化Alpha因子库

        参数:
            config (dict): 因子配置参数
        """
        self.logger = logging.getLogger('drl_trading')
        self.config = config or self._get_default_config()

        # 因子分类
        self.factor_categories = {
            'momentum': [],      # 动量因子
            'reversal': [],      # 反转因子
            'value': [],         # 价值因子
            'quality': [],       # 质量因子
            'growth': [],        # 成长因子
            'volatility': [],    # 波动率因子
            'volume': [],        # 成交量因子
            'technical': [],     # 技术指标因子
            'ml_derived': []     # 机器学习衍生因子
        }

        # 因子计算缓存
        self.factor_cache = {}

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'enable_cache': True,
            'cache_size_limit': 1000,
            'enable_factor_orthogonalization': True,
            'orthogonalization_method': 'pca',  # 'pca', 'gram_schmidt'
            'min_periods': 20,
            'max_periods': 252,
            'enable_robust_scaling': True,
            'outlier_threshold': 3.0,
            'enable_factor_decay_analysis': True
        }

    def generate_all_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        生成所有Alpha因子

        参数:
            data (pd.DataFrame): OHLCV数据

        返回:
            dict: 因子字典 {因子名: 因子值序列}
        """
        self.logger.info("开始生成Alpha因子库...")

        # 验证输入数据
        if not self._validate_input_data(data):
            self.logger.error("输入数据验证失败")
            return {}

        # 预处理数据
        processed_data = self._preprocess_data(data)

        # 生成各类因子
        all_factors = {}

        # 1. 动量因子
        momentum_factors = self._generate_momentum_factors(processed_data)
        all_factors.update(momentum_factors)
        self.factor_categories['momentum'] = list(momentum_factors.keys())

        # 2. 反转因子
        reversal_factors = self._generate_reversal_factors(processed_data)
        all_factors.update(reversal_factors)
        self.factor_categories['reversal'] = list(reversal_factors.keys())

        # 3. 价值因子
        value_factors = self._generate_value_factors(processed_data)
        all_factors.update(value_factors)
        self.factor_categories['value'] = list(value_factors.keys())

        # 4. 质量因子
        quality_factors = self._generate_quality_factors(processed_data)
        all_factors.update(quality_factors)
        self.factor_categories['quality'] = list(quality_factors.keys())

        # 5. 成长因子
        growth_factors = self._generate_growth_factors(processed_data)
        all_factors.update(growth_factors)
        self.factor_categories['growth'] = list(growth_factors.keys())

        # 6. 波动率因子
        volatility_factors = self._generate_volatility_factors(processed_data)
        all_factors.update(volatility_factors)
        self.factor_categories['volatility'] = list(volatility_factors.keys())

        # 7. 成交量因子
        volume_factors = self._generate_volume_factors(processed_data)
        all_factors.update(volume_factors)
        self.factor_categories['volume'] = list(volume_factors.keys())

        # 8. 技术指标因子
        technical_factors = self._generate_technical_factors(processed_data)
        all_factors.update(technical_factors)
        self.factor_categories['technical'] = list(technical_factors.keys())

        # 9. 机器学习衍生因子
        ml_factors = self._generate_ml_derived_factors(processed_data, all_factors)
        all_factors.update(ml_factors)
        self.factor_categories['ml_derived'] = list(ml_factors.keys())

        # 因子后处理
        processed_factors = self._postprocess_factors(all_factors)

        self.logger.info(f"成功生成 {len(processed_factors)} 个Alpha因子")

        return processed_factors

    def _validate_input_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']

        if data.empty:
            self.logger.error("输入数据为空")
            return False

        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            self.logger.error(f"缺少必需列: {missing_columns}")
            return False

        if len(data) < self.config['min_periods']:
            self.logger.error(f"数据长度不足，需要至少 {self.config['min_periods']} 个观测值")
            return False

        return True

    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """预处理数据"""
        processed_data = data.copy()

        # 确保数据类型正确
        numeric_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        for col in numeric_columns:
            if col in processed_data.columns:
                processed_data[col] = pd.to_numeric(processed_data[col], errors='coerce')

        # 计算基础衍生指标
        processed_data['典型价格'] = (processed_data['最高'] + processed_data['最低'] + processed_data['收盘']) / 3
        processed_data['加权收盘价'] = (processed_data['最高'] + processed_data['最低'] + 2 * processed_data['收盘']) / 4
        processed_data['价格范围'] = processed_data['最高'] - processed_data['最低']
        processed_data['收益率'] = processed_data['收盘'].pct_change()
        processed_data['对数收益率'] = np.log(processed_data['收盘'] / processed_data['收盘'].shift(1))

        # 计算VWAP
        if '成交额' in processed_data.columns:
            processed_data['VWAP'] = processed_data['成交额'] / processed_data['成交量']
        else:
            processed_data['VWAP'] = processed_data['典型价格']

        return processed_data

    def _generate_momentum_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成动量因子"""
        factors = {}
        close = data['收盘']

        # 价格动量因子
        for period in [5, 10, 20, 60, 120, 252]:
            factors[f'momentum_{period}'] = close / close.shift(period) - 1
            factors[f'log_momentum_{period}'] = np.log(close / close.shift(period))

        # 加权动量因子
        for period in [20, 60, 120]:
            weights = np.arange(1, period + 1)
            factors[f'weighted_momentum_{period}'] = close.rolling(window=period).apply(
                lambda x: np.sum(weights * x.pct_change().fillna(0)) / weights.sum(), raw=False
            )

        # 动量强度因子
        for period in [10, 20, 60]:
            returns = close.pct_change()
            factors[f'momentum_strength_{period}'] = returns.rolling(window=period).apply(
                lambda x: (x > 0).sum() / len(x), raw=False
            )

        # 相对强弱指数 (RSI)
        for period in [6, 14, 21]:
            factors[f'rsi_{period}'] = self._calculate_rsi(close, period)

        # 威廉指标 (Williams %R)
        for period in [14, 21]:
            factors[f'williams_r_{period}'] = self._calculate_williams_r(data, period)

        # 动量震荡指标
        for period in [12, 26]:
            factors[f'momentum_oscillator_{period}'] = (
                close - close.rolling(window=period).mean()
            ) / close.rolling(window=period).std()

        return factors

    def _generate_reversal_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成反转因子"""
        factors = {}
        close = data['收盘']
        high = data['最高']
        low = data['最低']
        volume = data['成交量']

        # 短期反转因子
        for period in [1, 3, 5, 10]:
            factors[f'reversal_{period}'] = -close.pct_change(period)

        # 超买超卖指标
        for period in [10, 20]:
            factors[f'overbought_oversold_{period}'] = (
                close - close.rolling(window=period).min()
            ) / (close.rolling(window=period).max() - close.rolling(window=period).min())

        # 价格位置指标
        for period in [20, 60]:
            factors[f'price_position_{period}'] = (
                close - close.rolling(window=period).mean()
            ) / close.rolling(window=period).std()

        # 布林带位置
        for period in [20, 40]:
            ma = close.rolling(window=period).mean()
            std = close.rolling(window=period).std()
            factors[f'bollinger_position_{period}'] = (close - ma) / (2 * std)

        # 反转强度指标
        for period in [5, 10, 20]:
            returns = close.pct_change()
            factors[f'reversal_strength_{period}'] = -returns.rolling(window=period).sum()

        return factors

    def _generate_value_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成价值因子"""
        factors = {}
        close = data['收盘']
        volume = data['成交量']

        # 价格相对指标
        for period in [20, 60, 252]:
            ma = close.rolling(window=period).mean()
            factors[f'price_to_ma_{period}'] = close / ma
            factors[f'ma_deviation_{period}'] = (close - ma) / ma

        # 价格分位数指标
        for period in [60, 120, 252]:
            factors[f'price_percentile_{period}'] = close.rolling(window=period).apply(
                lambda x: stats.percentileofscore(x, x.iloc[-1]) / 100, raw=False
            )

        # 价格效率指标
        for period in [20, 60]:
            price_change = abs(close - close.shift(period))
            path_length = abs(close.diff()).rolling(window=period).sum()
            factors[f'price_efficiency_{period}'] = price_change / path_length

        # 成交量加权价格指标
        for period in [10, 20, 60]:
            vwap = (close * volume).rolling(window=period).sum() / volume.rolling(window=period).sum()
            factors[f'price_to_vwap_{period}'] = close / vwap

        return factors

    def _generate_quality_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成质量因子"""
        factors = {}
        close = data['收盘']
        high = data['最高']
        low = data['最低']
        volume = data['成交量']

        # 价格稳定性指标
        for period in [20, 60, 120]:
            returns = close.pct_change()
            factors[f'price_stability_{period}'] = 1 / (returns.rolling(window=period).std() + 1e-8)

        # 趋势一致性指标
        for period in [10, 20, 60]:
            returns = close.pct_change()
            factors[f'trend_consistency_{period}'] = returns.rolling(window=period).apply(
                lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0.5, raw=False
            )

        # 价格质量指标（基于高低价差）
        for period in [20, 60]:
            price_range = high - low
            factors[f'price_quality_{period}'] = 1 / (price_range.rolling(window=period).mean() / close.rolling(window=period).mean() + 1e-8)

        # 成交量稳定性
        for period in [20, 60]:
            factors[f'volume_stability_{period}'] = 1 / (volume.rolling(window=period).std() / volume.rolling(window=period).mean() + 1e-8)

        return factors

    def _generate_growth_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成成长因子"""
        factors = {}
        close = data['收盘']
        volume = data['成交量']

        # 价格增长率
        for period in [20, 60, 120, 252]:
            factors[f'price_growth_{period}'] = (close / close.shift(period)) ** (252 / period) - 1

        # 成交量增长率
        for period in [20, 60, 120]:
            factors[f'volume_growth_{period}'] = (volume / volume.shift(period)) ** (252 / period) - 1

        # 增长加速度
        for period in [60, 120]:
            growth_rate = close.pct_change(period)
            factors[f'growth_acceleration_{period}'] = growth_rate.diff(period)

        # 增长稳定性
        for period in [60, 120, 252]:
            growth_rates = close.pct_change(20).rolling(window=period//20).apply(lambda x: x.std(), raw=False)
            factors[f'growth_stability_{period}'] = 1 / (growth_rates + 1e-8)

        return factors

    def _generate_volatility_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成波动率因子"""
        factors = {}
        close = data['收盘']
        high = data['最高']
        low = data['最低']

        # 历史波动率
        returns = close.pct_change()
        for period in [5, 10, 20, 60, 120]:
            factors[f'volatility_{period}'] = returns.rolling(window=period).std() * np.sqrt(252)

        # 真实波动幅度 (ATR)
        for period in [14, 20, 60]:
            factors[f'atr_{period}'] = self._calculate_atr(data, period)

        # 波动率比率
        for short, long in [(5, 20), (10, 60), (20, 120)]:
            vol_short = returns.rolling(window=short).std()
            vol_long = returns.rolling(window=long).std()
            factors[f'volatility_ratio_{short}_{long}'] = vol_short / vol_long

        # GARCH波动率（简化版）
        for period in [20, 60]:
            factors[f'garch_volatility_{period}'] = self._calculate_garch_volatility(returns, period)

        # 波动率偏度
        for period in [20, 60]:
            factors[f'volatility_skew_{period}'] = returns.rolling(window=period).skew()

        # 波动率峰度
        for period in [20, 60]:
            factors[f'volatility_kurtosis_{period}'] = returns.rolling(window=period).kurt()

        return factors

    def _generate_volume_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成成交量因子"""
        factors = {}
        close = data['收盘']
        volume = data['成交量']

        # 成交量比率
        for period in [5, 10, 20, 60]:
            factors[f'volume_ratio_{period}'] = volume / volume.rolling(window=period).mean()

        # 价量相关性
        for period in [10, 20, 60]:
            factors[f'price_volume_corr_{period}'] = close.rolling(window=period).corr(volume)

        # 成交量加权收益率
        for period in [5, 10, 20]:
            returns = close.pct_change()
            factors[f'volume_weighted_return_{period}'] = (returns * volume).rolling(window=period).sum() / volume.rolling(window=period).sum()

        # OBV (On Balance Volume)
        factors['obv'] = self._calculate_obv(close, volume)

        # 成交量震荡指标
        for period in [14, 21]:
            factors[f'volume_oscillator_{period}'] = (
                volume.rolling(window=period//2).mean() - volume.rolling(window=period).mean()
            ) / volume.rolling(window=period).mean()

        # 成交量强度指标
        for period in [10, 20]:
            price_change = close.diff()
            factors[f'volume_strength_{period}'] = (volume * np.sign(price_change)).rolling(window=period).sum()

        return factors

    def _generate_technical_factors(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成技术指标因子"""
        factors = {}
        close = data['收盘']
        high = data['最高']
        low = data['最低']
        volume = data['成交量']

        # MACD系列
        for fast, slow, signal in [(12, 26, 9), (5, 35, 5), (19, 39, 9)]:
            macd_line, macd_signal, macd_hist = self._calculate_macd(close, fast, slow, signal)
            factors[f'macd_{fast}_{slow}_{signal}'] = macd_line
            factors[f'macd_signal_{fast}_{slow}_{signal}'] = macd_signal
            factors[f'macd_histogram_{fast}_{slow}_{signal}'] = macd_hist

        # 随机指标 (Stochastic)
        for period in [14, 21]:
            factors[f'stoch_k_{period}'], factors[f'stoch_d_{period}'] = self._calculate_stochastic(data, period)

        # 商品通道指数 (CCI)
        for period in [14, 20]:
            factors[f'cci_{period}'] = self._calculate_cci(data, period)

        # 动向指数 (DMI)
        for period in [14, 21]:
            factors[f'dmi_plus_{period}'], factors[f'dmi_minus_{period}'], factors[f'adx_{period}'] = self._calculate_dmi(data, period)

        # 抛物线SAR
        factors['sar'] = self._calculate_sar(data)

        # 一目均衡表
        factors['ichimoku_tenkan'], factors['ichimoku_kijun'], factors['ichimoku_senkou_a'], factors['ichimoku_senkou_b'] = self._calculate_ichimoku(data)

        return factors

    # 辅助计算函数
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _calculate_williams_r(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算威廉指标"""
        high = data['最高']
        low = data['最低']
        close = data['收盘']

        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()

        return -100 * (highest_high - close) / (highest_high - lowest_low)

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算真实波动幅度"""
        high = data['最高']
        low = data['最低']
        close = data['收盘']

        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range.rolling(window=period).mean()

    def _calculate_garch_volatility(self, returns: pd.Series, period: int = 20) -> pd.Series:
        """计算GARCH波动率（简化版）"""
        # 简化的GARCH(1,1)模型
        alpha = 0.1
        beta = 0.8

        variance = pd.Series(index=returns.index, dtype=float)
        variance.iloc[0] = returns.var()

        for i in range(1, len(returns)):
            if pd.notna(returns.iloc[i-1]) and pd.notna(variance.iloc[i-1]):
                variance.iloc[i] = (
                    alpha * returns.iloc[i-1]**2 +
                    beta * variance.iloc[i-1] +
                    (1 - alpha - beta) * returns.var()
                )
            else:
                variance.iloc[i] = variance.iloc[i-1] if i > 0 else returns.var()

        return np.sqrt(variance * 252)  # 年化波动率

    def _calculate_obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """计算OBV指标"""
        price_change = close.diff()
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        return obv

    def _calculate_macd(self, close: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算MACD指标"""
        ema_fast = close.ewm(span=fast).mean()
        ema_slow = close.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        macd_signal = macd_line.ewm(span=signal).mean()
        macd_histogram = macd_line - macd_signal

        return macd_line, macd_signal, macd_histogram

    def _calculate_stochastic(self, data: pd.DataFrame, period: int = 14) -> Tuple[pd.Series, pd.Series]:
        """计算随机指标"""
        high = data['最高']
        low = data['最低']
        close = data['收盘']

        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()

        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        d_percent = k_percent.rolling(window=3).mean()

        return k_percent, d_percent

    def _calculate_cci(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算商品通道指数"""
        typical_price = (data['最高'] + data['最低'] + data['收盘']) / 3
        sma = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean())), raw=False
        )

        return (typical_price - sma) / (0.015 * mean_deviation)

    def _calculate_dmi(self, data: pd.DataFrame, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算动向指数"""
        high = data['最高']
        low = data['最低']
        close = data['收盘']

        # 计算真实波动幅度
        atr = self._calculate_atr(data, period)

        # 计算方向性移动
        dm_plus = pd.Series(index=data.index, dtype=float)
        dm_minus = pd.Series(index=data.index, dtype=float)

        for i in range(1, len(data)):
            high_diff = high.iloc[i] - high.iloc[i-1]
            low_diff = low.iloc[i-1] - low.iloc[i]

            if high_diff > low_diff and high_diff > 0:
                dm_plus.iloc[i] = high_diff
            else:
                dm_plus.iloc[i] = 0

            if low_diff > high_diff and low_diff > 0:
                dm_minus.iloc[i] = low_diff
            else:
                dm_minus.iloc[i] = 0

        # 计算方向性指标
        di_plus = 100 * dm_plus.rolling(window=period).mean() / atr
        di_minus = 100 * dm_minus.rolling(window=period).mean() / atr

        # 计算ADX
        dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = dx.rolling(window=period).mean()

        return di_plus, di_minus, adx

    def _calculate_sar(self, data: pd.DataFrame, acceleration: float = 0.02, maximum: float = 0.2) -> pd.Series:
        """计算抛物线SAR"""
        high = data['最高']
        low = data['最低']
        close = data['收盘']

        sar = pd.Series(index=data.index, dtype=float)
        trend = pd.Series(index=data.index, dtype=int)  # 1为上升，-1为下降
        af = pd.Series(index=data.index, dtype=float)
        ep = pd.Series(index=data.index, dtype=float)  # 极值点

        # 初始化
        sar.iloc[0] = low.iloc[0]
        trend.iloc[0] = 1
        af.iloc[0] = acceleration
        ep.iloc[0] = high.iloc[0]

        for i in range(1, len(data)):
            if trend.iloc[i-1] == 1:  # 上升趋势
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])

                if low.iloc[i] <= sar.iloc[i]:  # 趋势反转
                    trend.iloc[i] = -1
                    sar.iloc[i] = ep.iloc[i-1]
                    af.iloc[i] = acceleration
                    ep.iloc[i] = low.iloc[i]
                else:
                    trend.iloc[i] = 1
                    if high.iloc[i] > ep.iloc[i-1]:
                        ep.iloc[i] = high.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + acceleration, maximum)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]
            else:  # 下降趋势
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])

                if high.iloc[i] >= sar.iloc[i]:  # 趋势反转
                    trend.iloc[i] = 1
                    sar.iloc[i] = ep.iloc[i-1]
                    af.iloc[i] = acceleration
                    ep.iloc[i] = high.iloc[i]
                else:
                    trend.iloc[i] = -1
                    if low.iloc[i] < ep.iloc[i-1]:
                        ep.iloc[i] = low.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + acceleration, maximum)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]

        return sar

    def _calculate_ichimoku(self, data: pd.DataFrame) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
        """计算一目均衡表"""
        high = data['最高']
        low = data['最低']

        # 转换线 (9日)
        tenkan_sen = (high.rolling(window=9).max() + low.rolling(window=9).min()) / 2

        # 基准线 (26日)
        kijun_sen = (high.rolling(window=26).max() + low.rolling(window=26).min()) / 2

        # 先行带A
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)

        # 先行带B (52日)
        senkou_span_b = ((high.rolling(window=52).max() + low.rolling(window=52).min()) / 2).shift(26)

        return tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b

    def _winsorize_factor(self, factor: pd.Series, lower_percentile: float = 0.01, upper_percentile: float = 0.99) -> pd.Series:
        """因子去极值处理"""
        lower_bound = factor.quantile(lower_percentile)
        upper_bound = factor.quantile(upper_percentile)
        return factor.clip(lower=lower_bound, upper=upper_bound)

    def _standardize_factor(self, factor: pd.Series) -> pd.Series:
        """因子标准化"""
        if self.config['enable_robust_scaling']:
            # 使用稳健标准化
            median = factor.median()
            mad = (factor - median).abs().median()
            return (factor - median) / (mad * 1.4826)  # 1.4826是正态分布下的调整系数
        else:
            # 使用Z-score标准化
            return (factor - factor.mean()) / factor.std()

    def _generate_pca_factors(self, factor_df: pd.DataFrame, n_components: int = 5) -> Dict[str, pd.Series]:
        """生成PCA因子"""
        factors = {}

        try:
            # 标准化数据
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(factor_df.fillna(0))

            # PCA分解
            pca = PCA(n_components=n_components)
            pca_result = pca.fit_transform(scaled_data)

            # 创建PCA因子
            for i in range(n_components):
                factors[f'pca_factor_{i+1}'] = pd.Series(
                    pca_result[:, i],
                    index=factor_df.index
                )

        except Exception as e:
            self.logger.warning(f"PCA因子生成失败: {str(e)}")

        return factors

    def _generate_factor_momentum(self, factor_df: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成因子动量"""
        factors = {}

        for col in factor_df.columns[:10]:  # 只对前10个因子计算动量
            for period in [5, 20]:
                momentum = factor_df[col].diff(period)
                factors[f'{col}_momentum_{period}'] = momentum

        return factors

    def _generate_factor_mean_reversion(self, factor_df: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成因子均值回归"""
        factors = {}

        for col in factor_df.columns[:10]:  # 只对前10个因子计算均值回归
            for period in [20, 60]:
                ma = factor_df[col].rolling(window=period).mean()
                mean_reversion = factor_df[col] - ma
                factors[f'{col}_mean_reversion_{period}'] = mean_reversion

        return factors

    def _generate_factor_correlation(self, factor_df: pd.DataFrame) -> Dict[str, pd.Series]:
        """生成因子相关性指标"""
        factors = {}

        # 计算因子间的滚动相关性
        for i, col1 in enumerate(factor_df.columns[:5]):
            for j, col2 in enumerate(factor_df.columns[i+1:6]):
                corr = factor_df[col1].rolling(window=60).corr(factor_df[col2])
                factors[f'corr_{col1}_{col2}'] = corr

        return factors