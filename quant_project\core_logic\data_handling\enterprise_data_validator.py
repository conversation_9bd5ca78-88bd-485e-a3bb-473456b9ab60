"""
企业级数据验证器
实现多层次数据质量检查，符合生产环境标准
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import warnings
from scipy import stats
from sklearn.preprocessing import RobustScaler
import hashlib
import json

class DataQualityMetrics:
    """数据质量指标类"""

    def __init__(self):
        self.completeness_score = 0.0
        self.accuracy_score = 0.0
        self.consistency_score = 0.0
        self.timeliness_score = 0.0
        self.validity_score = 0.0
        self.overall_score = 0.0
        self.issues = []
        self.warnings = []

class EnterpriseDataValidator:
    """
    企业级数据验证器
    实现多层次数据质量检查框架
    """

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化企业级数据验证器

        参数:
            config (dict): 验证配置参数
        """
        self.logger = logging.getLogger('drl_trading')
        self.config = config or self._get_default_config()

        # 数据质量阈值
        self.quality_thresholds = {
            'completeness_min': 0.95,  # 最小完整性要求
            'accuracy_min': 0.98,      # 最小准确性要求
            'consistency_min': 0.95,   # 最小一致性要求
            'timeliness_max_delay': 1, # 最大延迟天数
            'validity_min': 0.99       # 最小有效性要求
        }

        # 审计日志
        self.audit_log = []

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'enable_syntax_check': True,
            'enable_semantic_check': True,
            'enable_business_rules_check': True,
            'enable_time_series_check': True,
            'enable_outlier_detection': True,
            'outlier_method': 'robust',  # 'zscore', 'iqr', 'robust', 'garch'
            'missing_value_threshold': 0.05,
            'price_change_threshold': 0.20,  # 20%单日涨跌幅阈值
            'volume_spike_threshold': 5.0,   # 成交量异常倍数
            'enable_audit_log': True
        }

    def validate_data(self, data: pd.DataFrame,
                     data_type: str = 'stock',
                     symbol: str = 'unknown') -> Tuple[bool, DataQualityMetrics]:
        """
        执行全面数据验证

        参数:
            data (pd.DataFrame): 待验证的数据
            data_type (str): 数据类型 ('stock', 'index', 'crypto', 'futures')
            symbol (str): 标的代码

        返回:
            tuple: (验证结果, 数据质量指标)
        """
        self.logger.info(f"开始企业级数据验证: {symbol} ({data_type})")

        # 初始化质量指标
        metrics = DataQualityMetrics()

        # 记录验证开始
        self._log_audit_event('validation_start', {
            'symbol': symbol,
            'data_type': data_type,
            'data_shape': data.shape,
            'date_range': f"{data.index[0]} to {data.index[-1]}" if len(data) > 0 else "empty"
        })

        try:
            # 1. 语法检查 (Syntax Check)
            syntax_valid, syntax_score = self._syntax_validation(data, metrics)
            metrics.validity_score = syntax_score

            if not syntax_valid:
                self.logger.error("数据语法验证失败")
                return False, metrics

            # 2. 语义检查 (Semantic Check)
            semantic_valid, semantic_score = self._semantic_validation(data, data_type, metrics)
            metrics.accuracy_score = semantic_score

            # 3. 业务规则检查 (Business Rules Check)
            business_valid, business_score = self._business_rules_validation(data, data_type, metrics)
            metrics.consistency_score = business_score

            # 4. 时间序列检查 (Time Series Check)
            timeseries_valid, timeseries_score = self._time_series_validation(data, metrics)
            metrics.timeliness_score = timeseries_score

            # 5. 完整性检查 (Completeness Check)
            completeness_valid, completeness_score = self._completeness_validation(data, metrics)
            metrics.completeness_score = completeness_score

            # 6. 异常值检测 (Outlier Detection)
            outlier_valid, outlier_info = self._advanced_outlier_detection(data, metrics)

            # 计算综合质量得分
            metrics.overall_score = self._calculate_overall_score(metrics)

            # 判断整体验证结果
            validation_passed = (
                syntax_valid and
                semantic_valid and
                business_valid and
                timeseries_valid and
                completeness_valid and
                metrics.overall_score >= 0.90
            )

            # 记录验证结果
            self._log_audit_event('validation_complete', {
                'symbol': symbol,
                'passed': validation_passed,
                'overall_score': metrics.overall_score,
                'issues_count': len(metrics.issues),
                'warnings_count': len(metrics.warnings)
            })

            self.logger.info(f"数据验证完成: {symbol}, 通过: {validation_passed}, 质量得分: {metrics.overall_score:.3f}")

            return validation_passed, metrics

        except Exception as e:
            self.logger.error(f"数据验证过程中出错: {str(e)}")
            metrics.issues.append(f"验证过程异常: {str(e)}")
            return False, metrics

    def _syntax_validation(self, data: pd.DataFrame, metrics: DataQualityMetrics) -> Tuple[bool, float]:
        """
        语法验证：检查数据结构和基本格式
        """
        issues = []
        score = 1.0

        # 检查数据是否为空
        if data.empty:
            issues.append("数据为空")
            score = 0.0

        # 检查必需列是否存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            issues.append(f"缺少必需列: {missing_columns}")
            score *= 0.5

        # 检查数据类型
        numeric_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        for col in numeric_columns:
            if col in data.columns:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    issues.append(f"列 {col} 不是数值类型")
                    score *= 0.9

        # 检查索引是否为日期类型
        if not isinstance(data.index, pd.DatetimeIndex):
            issues.append("索引不是日期时间类型")
            score *= 0.8

        # 更新指标
        metrics.issues.extend(issues)

        return len(issues) == 0, score

    def _semantic_validation(self, data: pd.DataFrame, data_type: str, metrics: DataQualityMetrics) -> Tuple[bool, float]:
        """
        语义验证：检查数据的业务含义是否正确
        """
        issues = []
        warnings = []
        score = 1.0

        if data.empty:
            return True, score

        # 检查价格是否为正
        price_columns = ['开盘', '最高', '最低', '收盘']
        for col in price_columns:
            if col in data.columns:
                negative_prices = (data[col] <= 0).sum()
                if negative_prices > 0:
                    issues.append(f"列 {col} 存在 {negative_prices} 个非正值")
                    score *= 0.8

        # 检查OHLC关系
        if all(col in data.columns for col in price_columns):
            # 最高价应该 >= 其他价格
            high_violations = (
                (data['最高'] < data['开盘']) |
                (data['最高'] < data['最低']) |
                (data['最高'] < data['收盘'])
            ).sum()

            # 最低价应该 <= 其他价格
            low_violations = (
                (data['最低'] > data['开盘']) |
                (data['最低'] > data['收盘'])
            ).sum()

            total_violations = high_violations + low_violations
            if total_violations > 0:
                violation_rate = total_violations / len(data)
                if violation_rate > 0.01:  # 超过1%的违规率
                    issues.append(f"OHLC关系违规率: {violation_rate:.2%}")
                    score *= (1 - violation_rate)
                else:
                    warnings.append(f"少量OHLC关系违规: {total_violations} 条")

        # 检查成交量
        if '成交量' in data.columns:
            negative_volume = (data['成交量'] < 0).sum()
            if negative_volume > 0:
                issues.append(f"成交量存在 {negative_volume} 个负值")
                score *= 0.9

        # 更新指标
        metrics.issues.extend(issues)
        metrics.warnings.extend(warnings)

        return len(issues) == 0, score

    def _business_rules_validation(self, data: pd.DataFrame, data_type: str, metrics: DataQualityMetrics) -> Tuple[bool, float]:
        """
        业务规则验证：检查是否符合金融市场业务规则
        """
        issues = []
        warnings = []
        score = 1.0

        if data.empty:
            return True, score

        # 检查价格变动幅度
        if '收盘' in data.columns:
            returns = data['收盘'].pct_change().dropna()
            extreme_returns = (abs(returns) > self.config['price_change_threshold']).sum()

            if extreme_returns > len(returns) * 0.05:  # 超过5%的极端收益率
                warnings.append(f"极端价格变动过多: {extreme_returns} 次")
                score *= 0.95

        # 检查成交量异常
        if '成交量' in data.columns:
            volume = data['成交量']
            volume_ma = volume.rolling(window=20).mean()
            volume_spikes = (volume > volume_ma * self.config['volume_spike_threshold']).sum()

            if volume_spikes > len(data) * 0.02:  # 超过2%的成交量异常
                warnings.append(f"成交量异常过多: {volume_spikes} 次")
                score *= 0.98

        # 根据数据类型进行特定检查
        if data_type == 'stock':
            score *= self._validate_stock_specific_rules(data, issues, warnings)
        elif data_type == 'crypto':
            score *= self._validate_crypto_specific_rules(data, issues, warnings)

        # 更新指标
        metrics.issues.extend(issues)
        metrics.warnings.extend(warnings)

        return len(issues) == 0, score

    def _time_series_validation(self, data: pd.DataFrame, metrics: DataQualityMetrics) -> Tuple[bool, float]:
        """
        时间序列验证：检查时间序列的连续性和及时性
        """
        issues = []
        warnings = []
        score = 1.0

        if data.empty:
            return True, score

        # 检查时间序列排序
        if not data.index.is_monotonic_increasing:
            issues.append("时间序列未按升序排列")
            score *= 0.8

        # 检查重复日期
        duplicate_dates = data.index.duplicated().sum()
        if duplicate_dates > 0:
            issues.append(f"存在 {duplicate_dates} 个重复日期")
            score *= 0.9

        # 检查数据及时性（最新数据的延迟）
        if len(data) > 0:
            latest_date = data.index[-1]
            current_date = pd.Timestamp.now().normalize()
            delay_days = (current_date - latest_date).days

            if delay_days > self.quality_thresholds['timeliness_max_delay']:
                warnings.append(f"数据延迟 {delay_days} 天")
                score *= max(0.5, 1 - delay_days * 0.1)

        # 检查数据频率一致性
        if len(data) > 2:
            freq_analysis = self._analyze_data_frequency(data)
            if freq_analysis['inconsistent_gaps'] > 0:
                warnings.append(f"数据频率不一致，存在 {freq_analysis['inconsistent_gaps']} 个异常间隔")
                score *= 0.95

        # 更新指标
        metrics.issues.extend(issues)
        metrics.warnings.extend(warnings)

        return len(issues) == 0, score

    def _completeness_validation(self, data: pd.DataFrame, metrics: DataQualityMetrics) -> Tuple[bool, float]:
        """
        完整性验证：检查数据的完整性
        """
        issues = []
        warnings = []
        score = 1.0

        if data.empty:
            issues.append("数据完全缺失")
            return False, 0.0

        # 计算缺失值比例
        total_cells = data.size
        missing_cells = data.isnull().sum().sum()
        missing_rate = missing_cells / total_cells if total_cells > 0 else 1.0

        if missing_rate > self.config['missing_value_threshold']:
            issues.append(f"缺失值比例过高: {missing_rate:.2%}")
            score *= (1 - missing_rate)
        elif missing_rate > 0:
            warnings.append(f"存在少量缺失值: {missing_rate:.2%}")
            score *= (1 - missing_rate * 0.5)

        # 检查关键列的完整性
        critical_columns = ['收盘', '成交量']
        for col in critical_columns:
            if col in data.columns:
                col_missing_rate = data[col].isnull().sum() / len(data)
                if col_missing_rate > 0.01:  # 关键列缺失率超过1%
                    issues.append(f"关键列 {col} 缺失率: {col_missing_rate:.2%}")
                    score *= 0.8

        # 更新指标
        metrics.issues.extend(issues)
        metrics.warnings.extend(warnings)

        return len(issues) == 0, score

    def _advanced_outlier_detection(self, data: pd.DataFrame, metrics: DataQualityMetrics) -> Tuple[bool, Dict]:
        """
        高级异常值检测：使用多种方法检测异常值
        """
        outlier_info = {
            'method_used': self.config['outlier_method'],
            'outliers_detected': 0,
            'outlier_columns': [],
            'outlier_details': {}
        }

        if data.empty:
            return True, outlier_info

        numeric_columns = data.select_dtypes(include=[np.number]).columns

        for col in numeric_columns:
            if col in data.columns:
                outliers = self._detect_outliers_by_method(
                    data[col],
                    method=self.config['outlier_method']
                )

                if outliers.sum() > 0:
                    outlier_rate = outliers.sum() / len(data)
                    outlier_info['outliers_detected'] += outliers.sum()
                    outlier_info['outlier_columns'].append(col)
                    outlier_info['outlier_details'][col] = {
                        'count': outliers.sum(),
                        'rate': outlier_rate
                    }

                    if outlier_rate > 0.05:  # 超过5%的异常值
                        metrics.warnings.append(f"列 {col} 异常值比例: {outlier_rate:.2%}")

        return True, outlier_info

    def _detect_outliers_by_method(self, series: pd.Series, method: str = 'robust') -> pd.Series:
        """
        使用指定方法检测异常值

        参数:
            series (pd.Series): 数据序列
            method (str): 检测方法 ('zscore', 'iqr', 'robust', 'garch')

        返回:
            pd.Series: 布尔序列，True表示异常值
        """
        if method == 'zscore':
            z_scores = np.abs(stats.zscore(series, nan_policy='omit'))
            return z_scores > 3

        elif method == 'iqr':
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            return (series < lower_bound) | (series > upper_bound)

        elif method == 'robust':
            # 使用稳健标准化方法
            scaler = RobustScaler()
            scaled_values = scaler.fit_transform(series.values.reshape(-1, 1)).flatten()
            return np.abs(scaled_values) > 3

        elif method == 'garch':
            # 基于GARCH模型的动态异常检测（简化版）
            returns = series.pct_change().dropna()
            rolling_std = returns.rolling(window=20).std()
            threshold = rolling_std.mean() + 3 * rolling_std.std()
            outliers = pd.Series(False, index=series.index)
            outliers.iloc[1:] = np.abs(returns) > threshold
            return outliers

        else:
            # 默认使用IQR方法
            return self._detect_outliers_by_method(series, 'iqr')

    def _validate_stock_specific_rules(self, data: pd.DataFrame, issues: List, warnings: List) -> float:
        """股票特定的业务规则验证"""
        score = 1.0

        # 检查涨跌停（假设10%涨跌停限制）
        if '收盘' in data.columns:
            returns = data['收盘'].pct_change().dropna()
            limit_hits = (abs(returns) >= 0.095).sum()  # 接近10%涨跌停

            if limit_hits > len(returns) * 0.1:  # 超过10%的交易日涨跌停
                warnings.append(f"涨跌停次数过多: {limit_hits} 次")
                score *= 0.95

        return score

    def _validate_crypto_specific_rules(self, data: pd.DataFrame, issues: List, warnings: List) -> float:
        """加密货币特定的业务规则验证"""
        score = 1.0

        # 加密货币可能有更大的波动性
        if '收盘' in data.columns:
            returns = data['收盘'].pct_change().dropna()
            extreme_returns = (abs(returns) > 0.5).sum()  # 50%的极端变动

            if extreme_returns > len(returns) * 0.02:  # 超过2%的极端变动
                warnings.append(f"极端价格变动: {extreme_returns} 次")
                score *= 0.98

        return score

    def _analyze_data_frequency(self, data: pd.DataFrame) -> Dict:
        """分析数据频率一致性"""
        if len(data) < 3:
            return {'inconsistent_gaps': 0, 'dominant_frequency': 'unknown'}

        # 计算相邻日期间隔
        date_diffs = data.index.to_series().diff().dropna()

        # 找出主导频率
        mode_diff = date_diffs.mode()
        dominant_frequency = mode_diff.iloc[0] if len(mode_diff) > 0 else pd.Timedelta(days=1)

        # 计算不一致的间隔数量
        inconsistent_gaps = (date_diffs != dominant_frequency).sum()

        return {
            'inconsistent_gaps': inconsistent_gaps,
            'dominant_frequency': str(dominant_frequency)
        }

    def _calculate_overall_score(self, metrics: DataQualityMetrics) -> float:
        """计算综合质量得分"""
        weights = {
            'completeness': 0.25,
            'accuracy': 0.25,
            'consistency': 0.20,
            'timeliness': 0.15,
            'validity': 0.15
        }

        overall_score = (
            weights['completeness'] * metrics.completeness_score +
            weights['accuracy'] * metrics.accuracy_score +
            weights['consistency'] * metrics.consistency_score +
            weights['timeliness'] * metrics.timeliness_score +
            weights['validity'] * metrics.validity_score
        )

        return overall_score

    def _log_audit_event(self, event_type: str, details: Dict) -> None:
        """记录审计事件"""
        if not self.config['enable_audit_log']:
            return

        audit_entry = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'details': details,
            'hash': self._generate_event_hash(event_type, details)
        }

        self.audit_log.append(audit_entry)

        # 限制审计日志大小
        if len(self.audit_log) > 1000:
            self.audit_log = self.audit_log[-500:]  # 保留最新500条

    def _generate_event_hash(self, event_type: str, details: Dict) -> str:
        """生成事件哈希值用于审计追踪"""
        content = f"{event_type}_{json.dumps(details, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()[:16]

    def get_audit_log(self) -> List[Dict]:
        """获取审计日志"""
        return self.audit_log.copy()

    def export_quality_report(self, metrics: DataQualityMetrics, symbol: str) -> Dict:
        """导出数据质量报告"""
        report = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'overall_score': metrics.overall_score,
            'dimension_scores': {
                'completeness': metrics.completeness_score,
                'accuracy': metrics.accuracy_score,
                'consistency': metrics.consistency_score,
                'timeliness': metrics.timeliness_score,
                'validity': metrics.validity_score
            },
            'issues': metrics.issues,
            'warnings': metrics.warnings,
            'quality_grade': self._get_quality_grade(metrics.overall_score),
            'recommendations': self._generate_recommendations(metrics)
        }

        return report

    def _get_quality_grade(self, score: float) -> str:
        """根据得分获取质量等级"""
        if score >= 0.95:
            return 'A+'
        elif score >= 0.90:
            return 'A'
        elif score >= 0.85:
            return 'B+'
        elif score >= 0.80:
            return 'B'
        elif score >= 0.70:
            return 'C'
        else:
            return 'D'

    def _generate_recommendations(self, metrics: DataQualityMetrics) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if metrics.completeness_score < 0.95:
            recommendations.append("建议检查数据源，补充缺失数据")

        if metrics.accuracy_score < 0.95:
            recommendations.append("建议验证数据准确性，检查数据采集流程")

        if metrics.consistency_score < 0.95:
            recommendations.append("建议检查业务规则一致性，修复数据异常")

        if metrics.timeliness_score < 0.90:
            recommendations.append("建议优化数据更新频率，减少数据延迟")

        if metrics.validity_score < 0.95:
            recommendations.append("建议检查数据格式和结构，确保符合规范")

        return recommendations