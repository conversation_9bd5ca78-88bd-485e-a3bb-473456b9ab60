"""
生产级回测引擎
实现真实的交易成本建模、市场微结构和风险管理
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
import warnings
from scipy import stats
from dataclasses import dataclass
from enum import Enum

class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"

@dataclass
class Order:
    """订单类"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: Optional[pd.Timestamp] = None
    order_id: Optional[str] = None

@dataclass
class Trade:
    """成交记录类"""
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    commission: float
    slippage: float
    market_impact: float
    timestamp: pd.Timestamp
    order_id: str
    trade_id: str

class MarketImpactModel:
    """市场冲击成本模型"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger('drl_trading')
    
    def calculate_impact(self, order: Order, market_data: pd.Series, 
                        avg_volume: float, volatility: float) -> float:
        """
        计算市场冲击成本
        
        参数:
            order: 订单对象
            market_data: 当前市场数据
            avg_volume: 平均成交量
            volatility: 波动率
            
        返回:
            float: 市场冲击成本（基点）
        """
        # 订单规模相对于平均成交量的比例
        volume_ratio = order.quantity / max(avg_volume, 1)
        
        # 基础冲击成本（线性模型）
        base_impact = self.config.get('base_impact_rate', 0.0001)  # 1bp
        
        # 规模冲击（平方根模型）
        size_impact = self.config.get('size_impact_coeff', 0.5) * np.sqrt(volume_ratio)
        
        # 波动率冲击
        volatility_impact = self.config.get('volatility_impact_coeff', 0.3) * volatility
        
        # 流动性冲击（当订单过大时）
        liquidity_penalty = 0
        if volume_ratio > self.config.get('liquidity_threshold', 0.1):  # 超过10%日均量
            liquidity_penalty = self.config.get('liquidity_penalty_rate', 0.002) * (volume_ratio - 0.1)
        
        total_impact = base_impact + size_impact + volatility_impact + liquidity_penalty
        
        # 买入为正冲击，卖出为负冲击
        return total_impact if order.side == OrderSide.BUY else -total_impact

class SlippageModel:
    """滑点模型"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger('drl_trading')
    
    def calculate_slippage(self, order: Order, market_data: pd.Series, 
                          bid_ask_spread: float, volatility: float) -> float:
        """
        计算滑点
        
        参数:
            order: 订单对象
            market_data: 当前市场数据
            bid_ask_spread: 买卖价差
            volatility: 波动率
            
        返回:
            float: 滑点（基点）
        """
        if order.order_type == OrderType.MARKET:
            # 市价单：承担半个买卖价差
            spread_cost = bid_ask_spread / 2
            
            # 波动率滑点
            volatility_slippage = self.config.get('volatility_slippage_coeff', 0.1) * volatility
            
            # 时间滑点（模拟执行延迟）
            time_slippage = self.config.get('execution_delay_slippage', 0.0001)  # 1bp
            
            total_slippage = spread_cost + volatility_slippage + time_slippage
            
        elif order.order_type == OrderType.LIMIT:
            # 限价单：可能获得负滑点（价格改善）
            improvement_prob = self.config.get('limit_order_improvement_prob', 0.3)
            if np.random.random() < improvement_prob:
                total_slippage = -self.config.get('limit_order_improvement', 0.0002)  # -2bp
            else:
                total_slippage = 0  # 按限价成交
        else:
            total_slippage = 0
        
        # 买入为正滑点，卖出为负滑点
        return total_slippage if order.side == OrderSide.BUY else -total_slippage

class CommissionModel:
    """佣金模型"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def calculate_commission(self, order: Order, fill_price: float) -> float:
        """
        计算佣金
        
        参数:
            order: 订单对象
            fill_price: 成交价格
            
        返回:
            float: 佣金金额
        """
        trade_value = order.quantity * fill_price
        
        # 基础佣金率
        base_rate = self.config.get('base_commission_rate', 0.0003)  # 3bp
        
        # 最小佣金
        min_commission = self.config.get('min_commission', 5.0)
        
        # 计算佣金
        commission = max(trade_value * base_rate, min_commission)
        
        # 大额交易折扣
        if trade_value > self.config.get('large_trade_threshold', 1000000):
            discount_rate = self.config.get('large_trade_discount', 0.8)
            commission *= discount_rate
        
        return commission

class ProductionBacktestEngine:
    """
    生产级回测引擎
    实现真实的交易成本建模和市场微结构
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化生产级回测引擎
        
        参数:
            config (dict): 回测配置
        """
        self.logger = logging.getLogger('drl_trading')
        self.config = config or self._get_default_config()
        
        # 初始化组件
        self.market_impact_model = MarketImpactModel(self.config.get('market_impact', {}))
        self.slippage_model = SlippageModel(self.config.get('slippage', {}))
        self.commission_model = CommissionModel(self.config.get('commission', {}))
        
        # 回测状态
        self.reset()
        
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'initial_capital': 1000000.0,
            'max_position_size': 0.1,  # 最大单票仓位10%
            'max_sector_exposure': 0.3,  # 最大行业暴露30%
            'max_leverage': 1.0,  # 无杠杆
            'enable_short_selling': False,
            'min_order_value': 1000.0,
            'max_order_value': 100000.0,
            'enable_risk_management': True,
            'enable_realistic_execution': True,
            'market_impact': {
                'base_impact_rate': 0.0001,
                'size_impact_coeff': 0.5,
                'volatility_impact_coeff': 0.3,
                'liquidity_threshold': 0.1,
                'liquidity_penalty_rate': 0.002
            },
            'slippage': {
                'volatility_slippage_coeff': 0.1,
                'execution_delay_slippage': 0.0001,
                'limit_order_improvement_prob': 0.3,
                'limit_order_improvement': 0.0002
            },
            'commission': {
                'base_commission_rate': 0.0003,
                'min_commission': 5.0,
                'large_trade_threshold': 1000000,
                'large_trade_discount': 0.8
            }
        }
    
    def reset(self):
        """重置回测状态"""
        self.capital = self.config['initial_capital']
        self.positions = {}  # {symbol: quantity}
        self.portfolio_values = pd.Series(dtype=float)
        self.trades = []
        self.orders = []
        self.current_date = None
        self.market_data_cache = {}
        
        # 风险指标
        self.max_drawdown = 0.0
        self.peak_value = self.capital
        self.daily_returns = pd.Series(dtype=float)
        
        # 性能统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_commission = 0.0
        self.total_slippage = 0.0
        self.total_market_impact = 0.0
        
        self.logger.info("回测引擎已重置")
    
    def run_backtest(self, data: pd.DataFrame, strategy: Any, 
                    benchmark: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        运行回测
        
        参数:
            data: 市场数据
            strategy: 交易策略
            benchmark: 基准数据
            
        返回:
            dict: 回测结果
        """
        self.logger.info("开始生产级回测...")
        
        try:
            # 验证数据
            if not self._validate_data(data):
                return {'status': 'error', 'message': '数据验证失败'}
            
            # 预处理数据
            processed_data = self._preprocess_data(data)
            
            # 执行回测循环
            for date in processed_data.index:
                self.current_date = date
                self._update_market_data_cache(processed_data, date)
                
                # 获取策略信号
                signals = self._get_strategy_signals(strategy, date)
                
                # 生成订单
                orders = self._generate_orders(signals, date)
                
                # 执行订单
                for order in orders:
                    self._execute_order(order, date)
                
                # 更新组合价值
                self._update_portfolio_value(date)
                
                # 风险检查
                if self.config['enable_risk_management']:
                    self._check_risk_limits(date)
            
            # 计算回测结果
            results = self._calculate_results(benchmark)
            
            self.logger.info("回测完成")
            return results
            
        except Exception as e:
            self.logger.error(f"回测过程中出错: {str(e)}")
            return {'status': 'error', 'message': str(e)}
