"""
专业级因子挖掘模块
符合顶级量化基金标准的Alpha因子发现和评估
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from scipy import stats
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import mutual_info_regression
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import warnings
from datetime import datetime, timedelta
import talib

class ProfessionalFactorMiner:
    """
    专业级因子挖掘器

    功能特性:
    1. 多维度因子构建
    2. 因子有效性评估
    3. 因子正交化处理
    4. 因子衰减分析
    5. 因子组合优化
    6. 风险因子识别
    """

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化专业因子挖掘器

        参数:
            config: 配置字典，包含挖掘参数
        """
        self.logger = logging.getLogger(self.__class__.__name__)

        # 默认配置
        self.config = {
            'lookback_periods': [5, 10, 20, 60, 120],  # 回看期
            'forward_periods': [1, 5, 10, 20],         # 前瞻期
            'min_ic_threshold': 0.02,                  # 最小IC阈值
            'min_ic_ir_threshold': 0.5,                # 最小IC_IR阈值
            'max_correlation_threshold': 0.8,          # 最大相关性阈值
            'factor_decay_periods': 20,                # 因子衰减分析期数
            'enable_neutralization': True,             # 启用中性化
            'neutralization_factors': ['market_cap', 'industry'],  # 中性化因子
            'enable_winsorization': True,              # 启用缩尾
            'winsorization_limits': (0.01, 0.99),     # 缩尾限制
            'enable_standardization': True,           # 启用标准化
            'min_factor_coverage': 0.8,               # 最小因子覆盖率
            'factor_categories': [                     # 因子分类
                'technical', 'fundamental', 'sentiment', 'macro'
            ]
        }

        if config:
            self.config.update(config)

        self.factor_library = {}
        self.factor_evaluations = {}
        self.factor_combinations = {}

    def mine_factors(self, data: pd.DataFrame,
                    returns: pd.Series = None,
                    market_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        执行完整的因子挖掘流程

        参数:
            data: 价格和成交量数据
            returns: 收益率数据
            market_data: 市场数据（可选）

        返回:
            因子挖掘结果
        """
        self.logger.info("开始专业级因子挖掘")

        # 1. 构建基础因子
        technical_factors = self._build_technical_factors(data)

        # 2. 构建基本面因子（如果有数据）
        fundamental_factors = self._build_fundamental_factors(data, market_data)

        # 3. 构建情绪因子
        sentiment_factors = self._build_sentiment_factors(data)

        # 4. 构建宏观因子
        macro_factors = self._build_macro_factors(data, market_data)

        # 5. 合并所有因子
        all_factors = pd.concat([
            technical_factors, fundamental_factors,
            sentiment_factors, macro_factors
        ], axis=1)

        # 6. 因子预处理
        processed_factors = self._preprocess_factors(all_factors)

        # 7. 计算收益率（如果未提供）
        if returns is None:
            returns = data['收盘'].pct_change()

        # 8. 因子有效性评估
        factor_evaluations = self._evaluate_factors(processed_factors, returns)

        # 9. 因子筛选
        selected_factors = self._select_factors(processed_factors, factor_evaluations)

        # 10. 因子正交化
        orthogonal_factors = self._orthogonalize_factors(selected_factors)

        # 11. 因子组合优化
        factor_combinations = self._optimize_factor_combinations(
            orthogonal_factors, returns
        )

        # 12. 生成挖掘报告
        mining_report = self._generate_mining_report(
            all_factors, selected_factors, factor_evaluations, factor_combinations
        )

        self.logger.info(f"因子挖掘完成，发现 {len(selected_factors.columns)} 个有效因子")

        return {
            'all_factors': all_factors,
            'selected_factors': selected_factors,
            'orthogonal_factors': orthogonal_factors,
            'factor_evaluations': factor_evaluations,
            'factor_combinations': factor_combinations,
            'mining_report': mining_report
        }

    def _build_technical_factors(self, data: pd.DataFrame) -> pd.DataFrame:
        """构建技术因子"""
        self.logger.debug("构建技术因子")

        factors = pd.DataFrame(index=data.index)

        # 价格因子
        factors['price_momentum_5'] = data['收盘'].pct_change(5)
        factors['price_momentum_20'] = data['收盘'].pct_change(20)
        factors['price_momentum_60'] = data['收盘'].pct_change(60)

        # 移动平均因子
        for period in [5, 10, 20, 60]:
            ma = data['收盘'].rolling(period).mean()
            factors[f'ma_ratio_{period}'] = data['收盘'] / ma - 1
            factors[f'ma_distance_{period}'] = (data['收盘'] - ma) / data['收盘'].rolling(60).std()

        # 波动率因子
        for period in [5, 20, 60]:
            factors[f'volatility_{period}'] = data['收盘'].pct_change().rolling(period).std()
            factors[f'volatility_ratio_{period}'] = (
                data['收盘'].pct_change().rolling(period).std() /
                data['收盘'].pct_change().rolling(period*3).std()
            )

        # 成交量因子
        if '成交量' in data.columns:
            for period in [5, 20, 60]:
                vol_ma = data['成交量'].rolling(period).mean()
                factors[f'volume_ratio_{period}'] = data['成交量'] / vol_ma

            # 量价关系因子
            factors['price_volume_corr_20'] = data['收盘'].pct_change().rolling(20).corr(
                data['成交量'].pct_change()
            )

        # RSI因子
        for period in [14, 30]:
            factors[f'rsi_{period}'] = talib.RSI(data['收盘'].values, timeperiod=period)

        # MACD因子
        macd, macd_signal, macd_hist = talib.MACD(data['收盘'].values)
        factors['macd'] = macd
        factors['macd_signal'] = macd_signal
        factors['macd_histogram'] = macd_hist

        # 布林带因子
        bb_upper, bb_middle, bb_lower = talib.BBANDS(data['收盘'].values)
        factors['bb_position'] = (data['收盘'] - bb_lower) / (bb_upper - bb_lower)
        factors['bb_width'] = (bb_upper - bb_lower) / bb_middle

        # 高低价因子
        factors['high_low_ratio'] = data['最高'] / data['最低'] - 1
        factors['close_position'] = (data['收盘'] - data['最低']) / (data['最高'] - data['最低'])

        # 跳空因子
        factors['gap'] = (data['开盘'] - data['收盘'].shift(1)) / data['收盘'].shift(1)

        return factors

    def _build_fundamental_factors(self, data: pd.DataFrame,
                                 market_data: pd.DataFrame = None) -> pd.DataFrame:
        """构建基本面因子"""
        self.logger.debug("构建基本面因子")

        factors = pd.DataFrame(index=data.index)

        # 如果没有基本面数据，构建基于价格的代理因子
        if market_data is None:
            # 市值代理（基于价格和成交量）
            if '成交量' in data.columns:
                factors['market_cap_proxy'] = data['收盘'] * data['成交量']
                factors['turnover_proxy'] = data['成交量'] / factors['market_cap_proxy']

            # 估值代理因子
            factors['pe_proxy'] = 1 / data['收盘'].pct_change(252)  # 简单PE代理
            factors['pb_proxy'] = data['收盘'] / data['收盘'].rolling(252).mean()  # 简单PB代理

        else:
            # 如果有基本面数据，构建真实基本面因子
            if 'market_cap' in market_data.columns:
                factors['market_cap'] = market_data['market_cap']
                factors['log_market_cap'] = np.log(market_data['market_cap'])

            if 'pe_ratio' in market_data.columns:
                factors['pe_ratio'] = market_data['pe_ratio']
                factors['pe_percentile'] = market_data['pe_ratio'].rolling(252).rank(pct=True)

            if 'pb_ratio' in market_data.columns:
                factors['pb_ratio'] = market_data['pb_ratio']
                factors['pb_percentile'] = market_data['pb_ratio'].rolling(252).rank(pct=True)

        return factors

    def _build_sentiment_factors(self, data: pd.DataFrame) -> pd.DataFrame:
        """构建情绪因子"""
        self.logger.debug("构建情绪因子")

        factors = pd.DataFrame(index=data.index)

        # 基于价格行为的情绪因子
        returns = data['收盘'].pct_change()

        # 连续上涨/下跌天数
        factors['consecutive_up'] = (returns > 0).astype(int).groupby(
            (returns <= 0).cumsum()
        ).cumsum()
        factors['consecutive_down'] = (returns < 0).astype(int).groupby(
            (returns >= 0).cumsum()
        ).cumsum()

        # 极端收益率频率
        for period in [20, 60]:
            returns_std = returns.rolling(period).std()
            factors[f'extreme_returns_{period}'] = (
                (returns.abs() > 2 * returns_std).rolling(period).sum()
            )

        # 收益率偏度和峰度
        for period in [20, 60]:
            factors[f'returns_skew_{period}'] = returns.rolling(period).skew()
            factors[f'returns_kurt_{period}'] = returns.rolling(period).kurt()

        # VIX代理（基于收益率波动）
        factors['vix_proxy'] = returns.rolling(20).std() * np.sqrt(252)

        return factors

    def _build_macro_factors(self, data: pd.DataFrame,
                           market_data: pd.DataFrame = None) -> pd.DataFrame:
        """构建宏观因子"""
        self.logger.debug("构建宏观因子")

        factors = pd.DataFrame(index=data.index)

        # 基于价格的宏观代理因子
        returns = data['收盘'].pct_change()

        # 市场趋势因子
        for period in [20, 60, 120]:
            factors[f'market_trend_{period}'] = returns.rolling(period).mean()
            factors[f'market_momentum_{period}'] = (
                returns.rolling(period).sum() / returns.rolling(period*2).sum()
            )

        # 市场波动因子
        factors['market_volatility'] = returns.rolling(20).std()
        factors['volatility_trend'] = (
            returns.rolling(20).std() / returns.rolling(60).std()
        )

        # 如果有市场数据，添加真实宏观因子
        if market_data is not None:
            if 'interest_rate' in market_data.columns:
                factors['interest_rate'] = market_data['interest_rate']
                factors['interest_rate_change'] = market_data['interest_rate'].diff()

            if 'inflation_rate' in market_data.columns:
                factors['inflation_rate'] = market_data['inflation_rate']

        return factors

    def _preprocess_factors(self, factors: pd.DataFrame) -> pd.DataFrame:
        """因子预处理"""
        self.logger.debug("因子预处理")

        processed_factors = factors.copy()

        # 1. 缩尾处理
        if self.config['enable_winsorization']:
            lower, upper = self.config['winsorization_limits']
            for col in processed_factors.columns:
                if processed_factors[col].dtype in ['float64', 'int64']:
                    processed_factors[col] = processed_factors[col].clip(
                        lower=processed_factors[col].quantile(lower),
                        upper=processed_factors[col].quantile(upper)
                    )

        # 2. 标准化
        if self.config['enable_standardization']:
            scaler = RobustScaler()
            for col in processed_factors.columns:
                if processed_factors[col].dtype in ['float64', 'int64']:
                    valid_data = processed_factors[col].dropna()
                    if len(valid_data) > 0:
                        processed_factors[col] = scaler.fit_transform(
                            processed_factors[col].values.reshape(-1, 1)
                        ).flatten()

        # 3. 处理缺失值
        processed_factors = processed_factors.fillna(method='ffill').fillna(0)

        # 4. 检查因子覆盖率
        coverage = processed_factors.notna().mean()
        low_coverage_factors = coverage[coverage < self.config['min_factor_coverage']].index
        if len(low_coverage_factors) > 0:
            self.logger.warning(f"低覆盖率因子: {list(low_coverage_factors)}")
            processed_factors = processed_factors.drop(columns=low_coverage_factors)

        return processed_factors

    def _evaluate_factors(self, factors: pd.DataFrame,
                         returns: pd.Series) -> Dict[str, Dict]:
        """评估因子有效性"""
        self.logger.debug("评估因子有效性")

        evaluations = {}

        for factor_name in factors.columns:
            factor_data = factors[factor_name].dropna()

            if len(factor_data) < 50:  # 数据点太少
                continue

            evaluation = {}

            # 计算不同前瞻期的IC
            for forward_period in self.config['forward_periods']:
                forward_returns = returns.shift(-forward_period)

                # 对齐数据
                aligned_factor, aligned_returns = factor_data.align(
                    forward_returns, join='inner'
                )

                if len(aligned_factor) > 20:
                    # IC (Information Coefficient)
                    ic = aligned_factor.corr(aligned_returns)

                    # Rank IC
                    rank_ic = aligned_factor.rank().corr(aligned_returns.rank())

                    evaluation[f'ic_{forward_period}'] = ic
                    evaluation[f'rank_ic_{forward_period}'] = rank_ic

            # 计算IC统计量
            ic_values = [evaluation.get(f'ic_{p}', 0) for p in self.config['forward_periods']]
            ic_values = [ic for ic in ic_values if not np.isnan(ic)]

            if ic_values:
                evaluation['ic_mean'] = np.mean(ic_values)
                evaluation['ic_std'] = np.std(ic_values)
                evaluation['ic_ir'] = evaluation['ic_mean'] / evaluation['ic_std'] if evaluation['ic_std'] > 0 else 0
                evaluation['ic_hit_rate'] = sum(1 for ic in ic_values if ic > 0) / len(ic_values)

            # 因子衰减分析
            decay_analysis = self._analyze_factor_decay(factor_data, returns)
            evaluation.update(decay_analysis)

            # 因子分布统计
            evaluation['factor_mean'] = factor_data.mean()
            evaluation['factor_std'] = factor_data.std()
            evaluation['factor_skew'] = factor_data.skew()
            evaluation['factor_kurt'] = factor_data.kurtosis()

            evaluations[factor_name] = evaluation

        return evaluations

    def _analyze_factor_decay(self, factor: pd.Series, returns: pd.Series) -> Dict:
        """分析因子衰减"""
        decay_periods = min(self.config['factor_decay_periods'], 20)
        decay_ics = []

        for period in range(1, decay_periods + 1):
            forward_returns = returns.shift(-period)
            aligned_factor, aligned_returns = factor.align(forward_returns, join='inner')

            if len(aligned_factor) > 10:
                ic = aligned_factor.corr(aligned_returns)
                decay_ics.append(ic if not np.isnan(ic) else 0)
            else:
                decay_ics.append(0)

        # 计算衰减率
        if len(decay_ics) > 1:
            decay_rate = np.polyfit(range(len(decay_ics)), decay_ics, 1)[0]
        else:
            decay_rate = 0

        return {
            'decay_ics': decay_ics,
            'decay_rate': decay_rate,
            'half_life': self._calculate_half_life(decay_ics)
        }

    def _calculate_half_life(self, decay_ics: List[float]) -> float:
        """计算因子半衰期"""
        if not decay_ics or decay_ics[0] == 0:
            return np.inf

        initial_ic = abs(decay_ics[0])
        half_ic = initial_ic / 2

        for i, ic in enumerate(decay_ics):
            if abs(ic) <= half_ic:
                return i + 1

        return len(decay_ics)