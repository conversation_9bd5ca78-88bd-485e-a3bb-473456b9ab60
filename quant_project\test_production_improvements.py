#!/usr/bin/env python3
"""
生产级改进验证测试
验证企业级数据验证器、Alpha因子库和生产级回测引擎
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('production_test')

def test_enterprise_data_validator():
    """测试企业级数据验证器"""
    print("=" * 80)
    print("测试1: 企业级数据验证器")
    print("=" * 80)
    
    try:
        from core_logic.data_handling.enterprise_data_validator import EnterpriseDataValidator, DataQualityMetrics
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', '2024-01-31', freq='D')
        test_data = pd.DataFrame({
            '开盘': np.random.uniform(100, 110, len(dates)),
            '最高': np.random.uniform(105, 115, len(dates)),
            '最低': np.random.uniform(95, 105, len(dates)),
            '收盘': np.random.uniform(100, 110, len(dates)),
            '成交量': np.random.uniform(1000000, 5000000, len(dates)),
            '成交额': np.random.uniform(100000000, 500000000, len(dates))
        }, index=dates)
        
        # 确保OHLC关系正确
        for i in range(len(test_data)):
            high = test_data.iloc[i]['最高']
            low = test_data.iloc[i]['最低']
            test_data.iloc[i, test_data.columns.get_loc('开盘')] = np.random.uniform(low, high)
            test_data.iloc[i, test_data.columns.get_loc('收盘')] = np.random.uniform(low, high)
        
        print(f"✅ 创建测试数据: {test_data.shape}")
        
        # 创建验证器
        validator = EnterpriseDataValidator()
        print("✅ 创建企业级数据验证器")
        
        # 执行验证
        is_valid, metrics = validator.validate_data(test_data, data_type='stock', symbol='TEST001')
        
        print(f"验证结果: {'通过' if is_valid else '失败'}")
        print(f"综合质量得分: {metrics.overall_score:.3f}")
        print(f"质量等级: {validator._get_quality_grade(metrics.overall_score)}")
        
        # 显示详细指标
        print("\n详细质量指标:")
        print(f"  完整性: {metrics.completeness_score:.3f}")
        print(f"  准确性: {metrics.accuracy_score:.3f}")
        print(f"  一致性: {metrics.consistency_score:.3f}")
        print(f"  及时性: {metrics.timeliness_score:.3f}")
        print(f"  有效性: {metrics.validity_score:.3f}")
        
        if metrics.issues:
            print(f"\n发现问题 ({len(metrics.issues)} 个):")
            for issue in metrics.issues[:5]:  # 只显示前5个
                print(f"  - {issue}")
        
        if metrics.warnings:
            print(f"\n警告信息 ({len(metrics.warnings)} 个):")
            for warning in metrics.warnings[:5]:  # 只显示前5个
                print(f"  - {warning}")
        
        # 导出质量报告
        report = validator.export_quality_report(metrics, 'TEST001')
        print(f"\n✅ 生成质量报告，包含 {len(report['recommendations'])} 条改进建议")
        
        return True
        
    except Exception as e:
        print(f"❌ 企业级数据验证器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_alpha_factor_library():
    """测试Alpha因子库"""
    print("\n" + "=" * 80)
    print("测试2: Alpha因子库")
    print("=" * 80)
    
    try:
        from core_logic.factor_mining.alpha_factor_library import AlphaFactorLibrary
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', '2024-01-31', freq='D')
        test_data = pd.DataFrame({
            '开盘': 100 + np.cumsum(np.random.normal(0, 0.02, len(dates))),
            '最高': 100 + np.cumsum(np.random.normal(0, 0.02, len(dates))),
            '最低': 100 + np.cumsum(np.random.normal(0, 0.02, len(dates))),
            '收盘': 100 + np.cumsum(np.random.normal(0, 0.02, len(dates))),
            '成交量': np.random.uniform(1000000, 5000000, len(dates)),
            '成交额': np.random.uniform(100000000, 500000000, len(dates))
        }, index=dates)
        
        # 确保OHLC关系正确
        for i in range(len(test_data)):
            base_price = test_data.iloc[i]['收盘']
            test_data.iloc[i, test_data.columns.get_loc('最高')] = base_price * np.random.uniform(1.0, 1.05)
            test_data.iloc[i, test_data.columns.get_loc('最低')] = base_price * np.random.uniform(0.95, 1.0)
            test_data.iloc[i, test_data.columns.get_loc('开盘')] = base_price * np.random.uniform(0.98, 1.02)
        
        print(f"✅ 创建测试数据: {test_data.shape}")
        
        # 创建因子库
        factor_library = AlphaFactorLibrary()
        print("✅ 创建Alpha因子库")
        
        # 生成因子（只测试部分因子以节省时间）
        print("开始生成因子...")
        
        # 测试动量因子
        momentum_factors = factor_library._generate_momentum_factors(factor_library._preprocess_data(test_data))
        print(f"✅ 生成动量因子: {len(momentum_factors)} 个")
        
        # 测试反转因子
        reversal_factors = factor_library._generate_reversal_factors(factor_library._preprocess_data(test_data))
        print(f"✅ 生成反转因子: {len(reversal_factors)} 个")
        
        # 测试价值因子
        value_factors = factor_library._generate_value_factors(factor_library._preprocess_data(test_data))
        print(f"✅ 生成价值因子: {len(value_factors)} 个")
        
        # 测试质量因子
        quality_factors = factor_library._generate_quality_factors(factor_library._preprocess_data(test_data))
        print(f"✅ 生成质量因子: {len(quality_factors)} 个")
        
        # 测试波动率因子
        volatility_factors = factor_library._generate_volatility_factors(factor_library._preprocess_data(test_data))
        print(f"✅ 生成波动率因子: {len(volatility_factors)} 个")
        
        # 测试成交量因子
        volume_factors = factor_library._generate_volume_factors(factor_library._preprocess_data(test_data))
        print(f"✅ 生成成交量因子: {len(volume_factors)} 个")
        
        total_factors = len(momentum_factors) + len(reversal_factors) + len(value_factors) + len(quality_factors) + len(volatility_factors) + len(volume_factors)
        print(f"\n✅ 总计生成 {total_factors} 个Alpha因子")
        
        # 检查因子质量
        valid_factors = 0
        for category, factors in [
            ('动量', momentum_factors),
            ('反转', reversal_factors), 
            ('价值', value_factors),
            ('质量', quality_factors),
            ('波动率', volatility_factors),
            ('成交量', volume_factors)
        ]:
            category_valid = 0
            for name, factor in factors.items():
                if factor is not None and not factor.empty and factor.notna().sum() > len(factor) * 0.5:
                    category_valid += 1
                    valid_factors += 1
            print(f"  {category}因子: {category_valid}/{len(factors)} 有效")
        
        print(f"\n✅ 有效因子比例: {valid_factors}/{total_factors} ({valid_factors/total_factors*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Alpha因子库测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_production_backtest_engine():
    """测试生产级回测引擎"""
    print("\n" + "=" * 80)
    print("测试3: 生产级回测引擎")
    print("=" * 80)
    
    try:
        from core_logic.backtest.production_backtest_engine import ProductionBacktestEngine, Order, OrderType, OrderSide
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', '2024-01-31', freq='D')
        test_data = pd.DataFrame({
            '开盘': 100 + np.cumsum(np.random.normal(0, 0.01, len(dates))),
            '最高': 100 + np.cumsum(np.random.normal(0, 0.01, len(dates))),
            '最低': 100 + np.cumsum(np.random.normal(0, 0.01, len(dates))),
            '收盘': 100 + np.cumsum(np.random.normal(0, 0.01, len(dates))),
            '成交量': np.random.uniform(1000000, 5000000, len(dates)),
            '成交额': np.random.uniform(100000000, 500000000, len(dates))
        }, index=dates)
        
        # 确保OHLC关系正确
        for i in range(len(test_data)):
            base_price = test_data.iloc[i]['收盘']
            test_data.iloc[i, test_data.columns.get_loc('最高')] = base_price * np.random.uniform(1.0, 1.02)
            test_data.iloc[i, test_data.columns.get_loc('最低')] = base_price * np.random.uniform(0.98, 1.0)
            test_data.iloc[i, test_data.columns.get_loc('开盘')] = base_price * np.random.uniform(0.99, 1.01)
        
        print(f"✅ 创建测试数据: {test_data.shape}")
        
        # 创建回测引擎
        config = {
            'initial_capital': 1000000.0,
            'enable_realistic_execution': True,
            'enable_risk_management': True
        }
        
        backtest_engine = ProductionBacktestEngine(config)
        print("✅ 创建生产级回测引擎")
        
        # 测试市场冲击模型
        order = Order(
            symbol='TEST001',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000
        )
        
        market_data = test_data.iloc[0]
        avg_volume = test_data['成交量'].mean()
        volatility = test_data['收盘'].pct_change().std()
        
        impact = backtest_engine.market_impact_model.calculate_impact(
            order, market_data, avg_volume, volatility
        )
        print(f"✅ 市场冲击模型测试: {impact:.4f} (基点)")
        
        # 测试滑点模型
        bid_ask_spread = 0.001  # 10bp买卖价差
        slippage = backtest_engine.slippage_model.calculate_slippage(
            order, market_data, bid_ask_spread, volatility
        )
        print(f"✅ 滑点模型测试: {slippage:.4f} (基点)")
        
        # 测试佣金模型
        fill_price = market_data['收盘']
        commission = backtest_engine.commission_model.calculate_commission(order, fill_price)
        print(f"✅ 佣金模型测试: {commission:.2f} 元")
        
        print(f"\n✅ 生产级回测引擎核心组件测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 生产级回测引擎测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始生产级改进验证测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    test_results = []
    
    test_results.append(test_enterprise_data_validator())
    test_results.append(test_alpha_factor_library())
    test_results.append(test_production_backtest_engine())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("生产级改进验证结果汇总")
    print("=" * 80)
    
    test_names = [
        "企业级数据验证器",
        "Alpha因子库",
        "生产级回测引擎"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results), 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i}. {name}: {status}")
    
    success_count = sum(test_results)
    total_count = len(test_results)
    success_rate = success_count / total_count * 100
    
    print(f"\n总体结果: {success_count}/{total_count} 测试通过 ({success_rate:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有生产级改进验证成功！系统已达到企业级标准。")
    else:
        print("⚠️  部分测试失败，需要进一步优化。")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
