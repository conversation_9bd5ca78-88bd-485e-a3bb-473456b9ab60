"""
专业级回测引擎
符合顶级量化基金标准的回测框架
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from abc import ABC, abstractmethod
import warnings

@dataclass
class Order:
    """订单类"""
    symbol: str
    quantity: float
    price: float
    timestamp: datetime
    order_type: str = 'market'  # market, limit
    side: str = 'buy'  # buy, sell
    status: str = 'pending'  # pending, filled, cancelled
    commission: float = 0.0
    slippage: float = 0.0

@dataclass
class Trade:
    """交易记录类"""
    symbol: str
    entry_time: datetime
    exit_time: datetime
    entry_price: float
    exit_price: float
    quantity: float
    pnl: float
    commission: float
    slippage: float
    holding_period: int

class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def check_position_limits(self, portfolio_value: float, 
                            position_value: float) -> bool:
        """检查仓位限制"""
        max_position_ratio = self.config.get('max_position_ratio', 0.1)
        return abs(position_value) / portfolio_value <= max_position_ratio
    
    def check_drawdown_limits(self, current_value: float, 
                            peak_value: float) -> bool:
        """检查回撤限制"""
        max_drawdown = self.config.get('max_drawdown', 0.2)
        current_drawdown = (peak_value - current_value) / peak_value
        return current_drawdown <= max_drawdown
    
    def check_volatility_limits(self, returns: pd.Series) -> bool:
        """检查波动率限制"""
        max_volatility = self.config.get('max_volatility', 0.3)
        current_vol = returns.rolling(20).std().iloc[-1] * np.sqrt(252)
        return current_vol <= max_volatility

class TransactionCostModel:
    """交易成本模型"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def calculate_commission(self, trade_value: float) -> float:
        """计算佣金"""
        commission_rate = self.config.get('commission_rate', 0.0003)
        min_commission = self.config.get('min_commission', 5.0)
        return max(trade_value * commission_rate, min_commission)
    
    def calculate_slippage(self, quantity: float, volume: float, 
                          volatility: float) -> float:
        """计算滑点"""
        base_slippage = self.config.get('base_slippage', 0.0001)
        volume_impact = self.config.get('volume_impact_factor', 0.1)
        volatility_impact = self.config.get('volatility_impact_factor', 0.5)
        
        # 市场冲击模型
        market_impact = volume_impact * (quantity / volume) ** 0.5
        volatility_adjustment = volatility_impact * volatility
        
        return base_slippage + market_impact + volatility_adjustment

class ProfessionalBacktestEngine:
    """
    专业级回测引擎
    
    功能特性:
    1. 精确的交易成本建模
    2. 实时风险管理
    3. 多资产组合回测
    4. 高频数据支持
    5. 事件驱动架构
    6. 详细的性能分析
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化专业回测引擎
        
        参数:
            config: 配置字典
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 默认配置
        self.config = {
            'initial_capital': 1000000.0,
            'commission_rate': 0.0003,
            'min_commission': 5.0,
            'base_slippage': 0.0001,
            'volume_impact_factor': 0.1,
            'volatility_impact_factor': 0.5,
            'max_position_ratio': 0.1,
            'max_drawdown': 0.2,
            'max_volatility': 0.3,
            'enable_risk_management': True,
            'enable_transaction_costs': True,
            'benchmark_symbol': None,
            'rebalance_frequency': 'daily',  # daily, weekly, monthly
            'lookback_window': 252,
            'enable_short_selling': False,
            'margin_requirement': 1.0,
        }
        
        if config:
            self.config.update(config)
        
        # 初始化组件
        self.risk_manager = RiskManager(self.config)
        self.cost_model = TransactionCostModel(self.config)
        
        # 状态变量
        self.reset()
    
    def reset(self):
        """重置回测状态"""
        self.capital = self.config['initial_capital']
        self.portfolio_value = self.config['initial_capital']
        self.positions = {}
        self.trades = []
        self.orders = []
        self.portfolio_history = []
        self.performance_metrics = {}
        self.current_date = None
        self.peak_value = self.config['initial_capital']
        
    def run_backtest(self, data: pd.DataFrame, strategy: Any,
                    benchmark_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        运行专业级回测
        
        参数:
            data: 市场数据
            strategy: 交易策略
            benchmark_data: 基准数据
            
        返回:
            回测结果
        """
        self.logger.info("开始专业级回测")
        self.reset()
        
        # 数据预处理
        processed_data = self._preprocess_data(data)
        
        # 初始化策略
        if hasattr(strategy, 'initialize'):
            strategy.initialize(self.config)
        
        # 回测主循环
        for date in processed_data.index:
            self.current_date = date
            
            # 更新市场数据
            current_market_data = processed_data.loc[date]
            
            # 获取策略信号
            signals = self._get_strategy_signals(strategy, current_market_data, date)
            
            # 风险检查
            if self.config['enable_risk_management']:
                signals = self._apply_risk_management(signals)
            
            # 生成订单
            orders = self._generate_orders(signals, current_market_data)
            
            # 执行订单
            for order in orders:
                self._execute_order(order, current_market_data)
            
            # 更新组合价值
            self._update_portfolio_value(current_market_data)
            
            # 记录状态
            self._record_state(date)
        
        # 计算性能指标
        performance_metrics = self._calculate_performance_metrics(benchmark_data)
        
        # 生成回测报告
        backtest_report = self._generate_backtest_report(performance_metrics)
        
        self.logger.info("回测完成")
        
        return {
            'portfolio_history': pd.DataFrame(self.portfolio_history),
            'trades': pd.DataFrame([trade.__dict__ for trade in self.trades]),
            'performance_metrics': performance_metrics,
            'backtest_report': backtest_report,
            'final_portfolio_value': self.portfolio_value,
            'total_return': (self.portfolio_value / self.config['initial_capital']) - 1
        }
    
    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """预处理数据"""
        processed_data = data.copy()
        
        # 确保数据按时间排序
        processed_data = processed_data.sort_index()
        
        # 计算技术指标
        if '收盘' in processed_data.columns:
            processed_data['returns'] = processed_data['收盘'].pct_change()
            processed_data['volatility'] = processed_data['returns'].rolling(20).std()
            processed_data['volume_ma'] = processed_data.get('成交量', pd.Series(index=processed_data.index)).rolling(20).mean()
        
        return processed_data
    
    def _get_strategy_signals(self, strategy: Any, market_data: pd.Series, 
                            date: datetime) -> Dict[str, float]:
        """获取策略信号"""
        try:
            if hasattr(strategy, 'generate_signals'):
                return strategy.generate_signals(market_data, date)
            elif hasattr(strategy, 'predict_action'):
                # 兼容旧接口
                state = {
                    'date': date,
                    'data': market_data,
                    'portfolio_value': self.portfolio_value,
                    'positions': self.positions.copy()
                }
                action = strategy.predict_action(state)
                return {'default': action if isinstance(action, (int, float)) else 0.0}
            else:
                return {}
        except Exception as e:
            self.logger.error(f"获取策略信号失败: {e}")
            return {}
    
    def _apply_risk_management(self, signals: Dict[str, float]) -> Dict[str, float]:
        """应用风险管理"""
        adjusted_signals = {}
        
        for symbol, signal in signals.items():
            # 检查仓位限制
            position_value = signal * self.portfolio_value
            if self.risk_manager.check_position_limits(self.portfolio_value, position_value):
                adjusted_signals[symbol] = signal
            else:
                # 调整信号强度
                max_position_value = self.portfolio_value * self.config['max_position_ratio']
                adjusted_signal = max_position_value / self.portfolio_value
                adjusted_signals[symbol] = adjusted_signal if signal > 0 else -adjusted_signal
                self.logger.warning(f"仓位限制调整: {symbol} {signal} -> {adjusted_signals[symbol]}")
        
        return adjusted_signals
