"""
专业级数据清洗模块
符合顶级量化基金标准的数据清洗和预处理功能
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy import stats
from sklearn.preprocessing import RobustScaler
import warnings
from datetime import datetime, timedelta

class ProfessionalDataCleaner:
    """
    专业级数据清洗器

    功能特性:
    1. 多层次数据验证
    2. 智能异常值检测和处理
    3. 缺失值高级处理策略
    4. 数据质量评分
    5. 公司行为调整
    6. 时间序列完整性检查
    """

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化专业数据清洗器

        参数:
            config: 配置字典，包含清洗参数
        """
        self.logger = logging.getLogger(self.__class__.__name__)

        # 默认配置
        self.config = {
            'outlier_method': 'iqr_mad',  # 异常值检测方法: iqr, zscore, mad, iqr_mad
            'outlier_threshold': 3.0,     # 异常值阈值
            'missing_threshold': 0.05,    # 缺失值比例阈值
            'price_jump_threshold': 0.15, # 价格跳跃阈值
            'volume_spike_threshold': 5.0, # 成交量异常倍数
            'enable_winsorization': True,  # 启用缩尾处理
            'winsorization_limits': (0.01, 0.99),  # 缩尾限制
            'enable_forward_fill': True,   # 启用前向填充
            'max_consecutive_missing': 5, # 最大连续缺失天数
            'data_quality_threshold': 0.8, # 数据质量阈值
            'enable_corporate_actions': True, # 启用公司行为调整
            'trading_calendar': 'china',   # 交易日历
        }

        if config:
            self.config.update(config)

        self.data_quality_report = {}
        self.cleaning_log = []

    def clean_data(self, data: pd.DataFrame, symbol: str = None) -> Tuple[pd.DataFrame, Dict]:
        """
        执行完整的数据清洗流程

        参数:
            data: 原始数据
            symbol: 标的代码

        返回:
            清洗后的数据和质量报告
        """
        self.logger.info(f"开始专业级数据清洗: {symbol}")

        # 重置清洗日志
        self.cleaning_log = []

        # 1. 数据结构验证
        cleaned_data = self._validate_data_structure(data.copy())

        # 2. 基础数据验证
        cleaned_data = self._validate_basic_data(cleaned_data)

        # 3. 时间序列完整性检查
        cleaned_data = self._check_time_series_integrity(cleaned_data)

        # 4. 异常值检测和处理
        cleaned_data = self._detect_and_handle_outliers(cleaned_data)

        # 5. 缺失值处理
        cleaned_data = self._handle_missing_values(cleaned_data)

        # 6. 价格跳跃检测
        cleaned_data = self._detect_price_jumps(cleaned_data)

        # 7. 成交量异常检测
        cleaned_data = self._detect_volume_anomalies(cleaned_data)

        # 8. 公司行为调整
        if self.config['enable_corporate_actions']:
            cleaned_data = self._adjust_corporate_actions(cleaned_data)

        # 9. 数据质量评估
        quality_score = self._calculate_data_quality_score(cleaned_data, data)

        # 10. 生成清洗报告
        cleaning_report = self._generate_cleaning_report(data, cleaned_data, quality_score)

        self.logger.info(f"数据清洗完成，质量评分: {quality_score:.3f}")

        return cleaned_data, cleaning_report

    def _validate_data_structure(self, data: pd.DataFrame) -> pd.DataFrame:
        """验证和标准化数据结构"""
        self.logger.debug("验证数据结构")

        # 确保索引是日期类型
        if not isinstance(data.index, pd.DatetimeIndex):
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data.set_index('date', inplace=True)
            else:
                try:
                    data.index = pd.to_datetime(data.index)
                except Exception as e:
                    raise ValueError(f"无法将索引转换为日期类型: {e}")

        # 标准化列名
        data = self._standardize_column_names(data)

        # 验证必要列存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")

        # 确保数据类型正确
        numeric_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')

        self.cleaning_log.append("数据结构验证完成")
        return data

    def _standardize_column_names(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        column_mapping = {
            'open': '开盘', 'high': '最高', 'low': '最低', 'close': '收盘',
            'volume': '成交量', 'amount': '成交额', 'turnover': '换手率',
            'pct_chg': '涨跌幅', 'pre_close': '前收盘'
        }

        # 重命名列
        renamed_columns = {}
        for col in data.columns:
            if col.lower() in column_mapping:
                renamed_columns[col] = column_mapping[col.lower()]

        if renamed_columns:
            data = data.rename(columns=renamed_columns)
            self.cleaning_log.append(f"标准化列名: {renamed_columns}")

        return data

    def _validate_basic_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """基础数据验证"""
        self.logger.debug("执行基础数据验证")

        # OHLC逻辑验证
        invalid_ohlc = (
            (data['最高'] < data['开盘']) |
            (data['最高'] < data['最低']) |
            (data['最高'] < data['收盘']) |
            (data['最低'] > data['开盘']) |
            (data['最低'] > data['收盘']) |
            (data['开盘'] <= 0) |
            (data['最高'] <= 0) |
            (data['最低'] <= 0) |
            (data['收盘'] <= 0) |
            (data['成交量'] < 0)
        )

        invalid_count = invalid_ohlc.sum()
        if invalid_count > 0:
            self.logger.warning(f"发现 {invalid_count} 条无效OHLC数据")
            # 删除无效数据
            data = data[~invalid_ohlc]
            self.cleaning_log.append(f"删除 {invalid_count} 条无效OHLC数据")

        return data

    def _check_time_series_integrity(self, data: pd.DataFrame) -> pd.DataFrame:
        """检查时间序列完整性"""
        self.logger.debug("检查时间序列完整性")

        # 检查重复日期
        duplicated_dates = data.index.duplicated()
        if duplicated_dates.any():
            dup_count = duplicated_dates.sum()
            self.logger.warning(f"发现 {dup_count} 个重复日期")
            data = data[~duplicated_dates]
            self.cleaning_log.append(f"删除 {dup_count} 个重复日期")

        # 排序数据
        data = data.sort_index()

        # 检查交易日缺失
        if len(data) > 1:
            date_diff = data.index.to_series().diff().dt.days
            large_gaps = date_diff > 7  # 超过7天的间隔
            if large_gaps.any():
                gap_count = large_gaps.sum()
                self.logger.warning(f"发现 {gap_count} 个大的时间间隔")
                self.cleaning_log.append(f"检测到 {gap_count} 个时间间隔 > 7天")

        return data

    def _detect_and_handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """检测和处理异常值"""
        self.logger.debug("检测和处理异常值")

        price_columns = ['开盘', '最高', '最低', '收盘']
        outlier_count = 0

        for col in price_columns:
            if col in data.columns:
                # 计算价格变化率
                pct_change = data[col].pct_change().abs()

                if self.config['outlier_method'] == 'iqr':
                    outliers = self._detect_outliers_iqr(pct_change)
                elif self.config['outlier_method'] == 'zscore':
                    outliers = self._detect_outliers_zscore(pct_change)
                elif self.config['outlier_method'] == 'mad':
                    outliers = self._detect_outliers_mad(pct_change)
                else:  # iqr_mad
                    outliers_iqr = self._detect_outliers_iqr(pct_change)
                    outliers_mad = self._detect_outliers_mad(pct_change)
                    outliers = outliers_iqr | outliers_mad

                if outliers.any():
                    outlier_count += outliers.sum()
                    if self.config['enable_winsorization']:
                        # 缩尾处理
                        lower, upper = self.config['winsorization_limits']
                        data[col] = data[col].clip(
                            lower=data[col].quantile(lower),
                            upper=data[col].quantile(upper)
                        )
                    else:
                        # 标记为缺失值
                        data.loc[outliers, col] = np.nan

        if outlier_count > 0:
            self.cleaning_log.append(f"处理 {outlier_count} 个异常值")

        return data

    def _detect_outliers_iqr(self, series: pd.Series) -> pd.Series:
        """使用IQR方法检测异常值"""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        return (series < lower_bound) | (series > upper_bound)

    def _detect_outliers_zscore(self, series: pd.Series) -> pd.Series:
        """使用Z-score方法检测异常值"""
        z_scores = np.abs(stats.zscore(series.dropna()))
        threshold = self.config['outlier_threshold']
        outliers = pd.Series(False, index=series.index)
        outliers.loc[series.dropna().index] = z_scores > threshold
        return outliers

    def _detect_outliers_mad(self, series: pd.Series) -> pd.Series:
        """使用MAD方法检测异常值"""
        median = series.median()
        mad = np.median(np.abs(series - median))
        threshold = self.config['outlier_threshold']
        return np.abs(series - median) > threshold * mad

    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        self.logger.debug("处理缺失值")

        # 计算缺失值比例
        missing_ratio = data.isnull().sum() / len(data)
        high_missing_cols = missing_ratio[missing_ratio > self.config['missing_threshold']].index

        if len(high_missing_cols) > 0:
            self.logger.warning(f"高缺失值列: {list(high_missing_cols)}")
            self.cleaning_log.append(f"高缺失值列: {list(high_missing_cols)}")

        # 处理价格数据缺失值
        price_columns = ['开盘', '最高', '最低', '收盘']
        for col in price_columns:
            if col in data.columns:
                missing_mask = data[col].isnull()
                if missing_mask.any():
                    if self.config['enable_forward_fill']:
                        # 前向填充
                        data[col] = data[col].fillna(method='ffill')
                        # 后向填充剩余的
                        data[col] = data[col].fillna(method='bfill')
                    else:
                        # 使用插值
                        data[col] = data[col].interpolate(method='linear')

        # 处理成交量缺失值
        if '成交量' in data.columns:
            volume_missing = data['成交量'].isnull()
            if volume_missing.any():
                # 成交量缺失用0填充
                data['成交量'] = data['成交量'].fillna(0)
                self.cleaning_log.append(f"成交量缺失值用0填充: {volume_missing.sum()}个")

        return data

    def _detect_price_jumps(self, data: pd.DataFrame) -> pd.DataFrame:
        """检测价格跳跃"""
        self.logger.debug("检测价格跳跃")

        threshold = self.config['price_jump_threshold']
        price_columns = ['开盘', '最高', '最低', '收盘']
        jump_count = 0

        for col in price_columns:
            if col in data.columns:
                pct_change = data[col].pct_change().abs()
                jumps = pct_change > threshold

                if jumps.any():
                    jump_dates = data.index[jumps]
                    jump_count += len(jump_dates)
                    self.logger.warning(f"{col} 发现 {len(jump_dates)} 个价格跳跃")

        if jump_count > 0:
            self.cleaning_log.append(f"检测到 {jump_count} 个价格跳跃")

        return data

    def _detect_volume_anomalies(self, data: pd.DataFrame) -> pd.DataFrame:
        """检测成交量异常"""
        self.logger.debug("检测成交量异常")

        if '成交量' not in data.columns:
            return data

        # 计算成交量移动平均
        volume_ma = data['成交量'].rolling(window=20).mean()
        volume_ratio = data['成交量'] / volume_ma

        # 检测异常成交量
        threshold = self.config['volume_spike_threshold']
        volume_spikes = volume_ratio > threshold

        if volume_spikes.any():
            spike_count = volume_spikes.sum()
            self.logger.warning(f"发现 {spike_count} 个成交量异常")
            self.cleaning_log.append(f"检测到 {spike_count} 个成交量异常")

        return data

    def _adjust_corporate_actions(self, data: pd.DataFrame) -> pd.DataFrame:
        """调整公司行为（分红、拆股等）"""
        self.logger.debug("调整公司行为")

        # 检测可能的除权除息
        price_columns = ['开盘', '最高', '最低', '收盘']

        for col in price_columns:
            if col in data.columns:
                # 检测大幅价格下跌（可能的除权）
                pct_change = data[col].pct_change()
                large_drops = pct_change < -0.1  # 10%以上下跌

                if large_drops.any():
                    drop_dates = data.index[large_drops]
                    self.logger.info(f"{col} 检测到可能的除权日期: {len(drop_dates)}个")

        self.cleaning_log.append("公司行为调整检查完成")
        return data

    def _calculate_data_quality_score(self, cleaned_data: pd.DataFrame,
                                    original_data: pd.DataFrame) -> float:
        """计算数据质量评分"""
        score_components = {}

        # 1. 完整性评分 (40%)
        completeness = 1 - (cleaned_data.isnull().sum().sum() / cleaned_data.size)
        score_components['completeness'] = completeness * 0.4

        # 2. 一致性评分 (30%)
        # 检查OHLC逻辑一致性
        consistency_checks = [
            (cleaned_data['最高'] >= cleaned_data['开盘']).all(),
            (cleaned_data['最高'] >= cleaned_data['最低']).all(),
            (cleaned_data['最高'] >= cleaned_data['收盘']).all(),
            (cleaned_data['最低'] <= cleaned_data['开盘']).all(),
            (cleaned_data['最低'] <= cleaned_data['收盘']).all(),
        ]
        consistency = sum(consistency_checks) / len(consistency_checks)
        score_components['consistency'] = consistency * 0.3

        # 3. 准确性评分 (20%)
        # 基于异常值比例
        outlier_logs = [log for log in self.cleaning_log if '异常值' in log]
        total_outliers = sum([int(log.split()[1]) for log in outlier_logs if log.split()[1].isdigit()])
        accuracy = max(0, 1 - (total_outliers / len(original_data)))
        score_components['accuracy'] = accuracy * 0.2

        # 4. 时效性评分 (10%)
        # 基于数据的时间连续性
        if len(cleaned_data) > 1:
            date_gaps = cleaned_data.index.to_series().diff().dt.days
            large_gaps = (date_gaps > 7).sum()
            timeliness = max(0, 1 - (large_gaps / len(cleaned_data)))
        else:
            timeliness = 1.0
        score_components['timeliness'] = timeliness * 0.1

        total_score = sum(score_components.values())
        self.data_quality_report = score_components

        return total_score

    def _generate_cleaning_report(self, original_data: pd.DataFrame,
                                cleaned_data: pd.DataFrame,
                                quality_score: float) -> Dict:
        """生成清洗报告"""
        report = {
            'original_shape': original_data.shape,
            'cleaned_shape': cleaned_data.shape,
            'data_quality_score': quality_score,
            'quality_components': self.data_quality_report,
            'cleaning_operations': self.cleaning_log,
            'missing_values_before': original_data.isnull().sum().to_dict(),
            'missing_values_after': cleaned_data.isnull().sum().to_dict(),
            'date_range': {
                'start': cleaned_data.index.min(),
                'end': cleaned_data.index.max(),
                'total_days': len(cleaned_data)
            },
            'recommendations': self._generate_recommendations(quality_score)
        }

        return report

    def _generate_recommendations(self, quality_score: float) -> List[str]:
        """生成数据质量改进建议"""
        recommendations = []

        if quality_score < 0.6:
            recommendations.append("数据质量较低，建议检查数据源")
        elif quality_score < 0.8:
            recommendations.append("数据质量中等，建议进一步清洗")
        else:
            recommendations.append("数据质量良好")

        # 基于清洗日志生成具体建议
        for log in self.cleaning_log:
            if '异常值' in log:
                recommendations.append("建议检查异常值检测参数")
            if '缺失值' in log:
                recommendations.append("建议改进缺失值处理策略")
            if '价格跳跃' in log:
                recommendations.append("建议验证价格跳跃的合理性")

        return recommendations