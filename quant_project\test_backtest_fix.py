"""
测试回测错误修复
验证回测引擎的数据结构兼容性
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # 创建基础OHLCV数据
    np.random.seed(42)
    base_price = 100
    
    data = pd.DataFrame(index=dates)
    data['开盘'] = base_price + np.cumsum(np.random.randn(len(dates)) * 0.02)
    data['最高'] = data['开盘'] + np.random.rand(len(dates)) * 2
    data['最低'] = data['开盘'] - np.random.rand(len(dates)) * 2
    data['收盘'] = data['开盘'] + np.random.randn(len(dates)) * 0.5
    data['成交量'] = np.random.randint(1000000, 10000000, len(dates))
    
    # 确保OHLC逻辑正确
    data['最高'] = np.maximum(data['最高'], np.maximum(data['开盘'], data['收盘']))
    data['最低'] = np.minimum(data['最低'], np.minimum(data['开盘'], data['收盘']))
    
    return data

def test_data_structure_compatibility():
    """测试数据结构兼容性"""
    print("=== 测试数据结构兼容性 ===")
    
    # 创建测试数据
    data = create_test_data()
    print(f"原始数据形状: {data.shape}")
    print(f"数据列: {list(data.columns)}")
    print(f"数据类型: {data.dtypes}")
    
    # 测试不同的数据结构
    print("\n1. 测试普通DataFrame结构")
    test_normal_dataframe(data)
    
    print("\n2. 测试MultiIndex结构")
    test_multiindex_dataframe(data)
    
    print("\n3. 测试混合列名结构")
    test_mixed_columns(data)

def test_normal_dataframe(data):
    """测试普通DataFrame结构"""
    try:
        # 模拟MockStrategy的数据访问
        for i, (date, row) in enumerate(data.iterrows()):
            if i > 5:  # 只测试前几行
                break
                
            # 测试不同的访问方式
            if '收盘' in row.index:
                price = row['收盘']
                print(f"  日期: {date}, 收盘价: {price:.2f}")
            else:
                print(f"  日期: {date}, 未找到收盘价列")
        
        print("  ✅ 普通DataFrame结构测试通过")
        
    except Exception as e:
        print(f"  ❌ 普通DataFrame结构测试失败: {e}")

def test_multiindex_dataframe(data):
    """测试MultiIndex结构"""
    try:
        # 创建MultiIndex结构
        symbol = 'TEST001'
        multi_columns = pd.MultiIndex.from_product([[symbol], data.columns])
        multi_data = data.copy()
        multi_data.columns = multi_columns
        
        print(f"  MultiIndex列: {multi_data.columns.tolist()[:3]}...")
        
        # 测试数据访问
        for i, (date, row) in enumerate(multi_data.iterrows()):
            if i > 5:
                break
                
            # 测试MultiIndex访问
            if (symbol, '收盘') in row.index:
                price = row[(symbol, '收盘')]
                print(f"  日期: {date}, 收盘价: {price:.2f}")
            else:
                print(f"  日期: {date}, 未找到收盘价列")
        
        print("  ✅ MultiIndex结构测试通过")
        
    except Exception as e:
        print(f"  ❌ MultiIndex结构测试失败: {e}")

def test_mixed_columns(data):
    """测试混合列名结构"""
    try:
        # 创建混合列名
        mixed_data = data.copy()
        mixed_data.columns = ['open', 'high', 'low', 'close', 'volume']
        
        print(f"  混合列名: {list(mixed_data.columns)}")
        
        # 测试列名映射
        column_mapping = {
            'open': '开盘', 'high': '最高', 'low': '最低', 
            'close': '收盘', 'volume': '成交量'
        }
        
        for i, (date, row) in enumerate(mixed_data.iterrows()):
            if i > 5:
                break
                
            # 测试英文列名访问
            if 'close' in row.index:
                price = row['close']
                print(f"  日期: {date}, 收盘价(close): {price:.2f}")
            
            # 测试映射后访问
            mapped_price = None
            for eng_col, chn_col in column_mapping.items():
                if eng_col in row.index and eng_col == 'close':
                    mapped_price = row[eng_col]
                    break
            
            if mapped_price is not None:
                print(f"  日期: {date}, 映射收盘价: {mapped_price:.2f}")
        
        print("  ✅ 混合列名结构测试通过")
        
    except Exception as e:
        print(f"  ❌ 混合列名结构测试失败: {e}")

def test_mock_strategy():
    """测试修复后的MockStrategy"""
    print("\n=== 测试修复后的MockStrategy ===")
    
    try:
        # 导入修复后的回测模块
        from ui_components.backtest_analysis import MockStrategy
        
        # 创建测试数据
        data = create_test_data()
        
        # 创建策略实例
        strategy = MockStrategy({'model_type': 'test'})
        
        # 测试不同数据结构
        test_cases = [
            ("普通DataFrame", data),
            ("MultiIndex", create_multiindex_data(data)),
        ]
        
        for case_name, test_data in test_cases:
            print(f"\n测试案例: {case_name}")
            
            for i, (date, row) in enumerate(test_data.iterrows()):
                if i > 3:  # 只测试前几行
                    break
                
                try:
                    # 构建状态
                    state = {
                        'date': date,
                        'data': row
                    }
                    
                    # 调用策略
                    action = strategy.predict_action(state)
                    print(f"  日期: {date}, 动作: {action}")
                    
                except Exception as e:
                    print(f"  ❌ 策略调用失败: {e}")
                    break
            else:
                print(f"  ✅ {case_name} 测试通过")
        
    except ImportError as e:
        print(f"❌ 无法导入MockStrategy: {e}")
    except Exception as e:
        print(f"❌ MockStrategy测试失败: {e}")

def create_multiindex_data(data):
    """创建MultiIndex数据"""
    symbol = 'TEST001'
    multi_columns = pd.MultiIndex.from_product([[symbol], data.columns])
    multi_data = data.copy()
    multi_data.columns = multi_columns
    return multi_data

def test_professional_modules():
    """测试专业级模块"""
    print("\n=== 测试专业级模块 ===")
    
    # 测试专业数据清洗器
    try:
        from core_logic.data_handling.professional_data_cleaner import ProfessionalDataCleaner
        
        cleaner = ProfessionalDataCleaner()
        test_data = create_test_data()
        
        # 添加一些异常值进行测试
        test_data.iloc[10, 0] = test_data.iloc[10, 0] * 10  # 异常开盘价
        test_data.iloc[20:25, 1] = np.nan  # 缺失值
        
        cleaned_data, report = cleaner.clean_data(test_data, 'TEST001')
        
        print(f"  ✅ 专业数据清洗器测试通过")
        print(f"  数据质量评分: {report['data_quality_score']:.3f}")
        print(f"  清洗操作: {len(report['cleaning_operations'])}项")
        
    except Exception as e:
        print(f"  ❌ 专业数据清洗器测试失败: {e}")
    
    # 测试专业因子挖掘器
    try:
        from core_logic.factor_mining.professional_factor_miner import ProfessionalFactorMiner
        
        miner = ProfessionalFactorMiner()
        test_data = create_test_data()
        
        # 只测试技术因子构建
        technical_factors = miner._build_technical_factors(test_data)
        
        print(f"  ✅ 专业因子挖掘器测试通过")
        print(f"  技术因子数量: {len(technical_factors.columns)}")
        print(f"  因子示例: {list(technical_factors.columns)[:5]}")
        
    except Exception as e:
        print(f"  ❌ 专业因子挖掘器测试失败: {e}")

def main():
    """主测试函数"""
    print("开始回测错误修复验证")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 测试数据结构兼容性
        test_data_structure_compatibility()
        
        # 测试修复后的策略
        test_mock_strategy()
        
        # 测试专业级模块
        test_professional_modules()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        print("\n主要修复:")
        print("1. ✅ 修复了回测中的数据结构兼容性问题")
        print("2. ✅ 增强了MockStrategy的错误处理能力")
        print("3. ✅ 添加了专业级数据清洗模块")
        print("4. ✅ 添加了专业级因子挖掘模块")
        print("5. ✅ 创建了系统增强计划文档")
        
        print("\n建议下一步:")
        print("1. 在UI中测试修复后的回测功能")
        print("2. 逐步集成专业级模块")
        print("3. 按照增强计划进行系统升级")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
